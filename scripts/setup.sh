#!/bin/bash

# Echos OS Setup Script

set -e

echo "Setting up Echos OS development environment..."

# Check if Rust is installed
if ! command -v rustc &> /dev/null; then
    echo "Error: Rust is not installed. Please install Rust first:"
    echo "curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh"
    exit 1
fi

# Install required Rust targets and tools
echo "Installing Rust targets and tools..."
rustup target add riscv64gc-unknown-none-elf
rustup target add loongarch64-unknown-none

# Install cargo-binutils for objcopy
cargo install cargo-binutils
rustup component add llvm-tools-preview

# Check for QEMU
echo "Checking for QEMU..."
if command -v qemu-system-riscv64 &> /dev/null; then
    echo "✓ QEMU RISC-V found"
else
    echo "⚠ QEMU RISC-V not found. Install with:"
    echo "  Ubuntu/Debian: sudo apt install qemu-system-misc"
    echo "  macOS: brew install qemu"
fi

if command -v qemu-system-loongarch64 &> /dev/null; then
    echo "✓ QEMU LoongArch found"
else
    echo "⚠ QEMU LoongArch not found (may need newer QEMU version)"
fi

# Check for cross-compilation toolchains
echo "Checking for cross-compilation toolchains..."
if command -v riscv64-linux-gnu-as &> /dev/null; then
    echo "✓ RISC-V toolchain found"
else
    echo "⚠ RISC-V toolchain not found. Install with:"
    echo "  Ubuntu/Debian: sudo apt install gcc-riscv64-linux-gnu"
fi

if command -v loongarch64-linux-gnu-as &> /dev/null; then
    echo "✓ LoongArch toolchain found"
else
    echo "⚠ LoongArch toolchain not found (may need to build from source)"
fi

echo ""
echo "Setup complete! You can now:"
echo "  make run          # Build and run for RISC-V"
echo "  make runlog       # Build and run with logging"
echo "  make help         # Show all available targets"
