#!/bin/bash

# Script to extract the first 3 exceptions from QEMU log files
# Usage: ./extract_exceptions.sh <log_file>
# All QEMU exceptions start with "do_raise_exception:"

set -e

# Function to display usage
usage() {
    echo "Usage: $0 <log_file>"
    echo "Extract the first 3 exceptions from QEMU log file"
    echo "Exceptions are identified by lines starting with 'do_raise_exception:'"
    echo ""
    echo "Examples:"
    echo "  $0 qemu.log"
    echo "  $0 qemu-loongarch.log"
    echo "  $0 /path/to/qemu.log"
    exit 1
}

# Function to print colored output
print_header() {
    echo -e "\033[1;34m=== $1 ===\033[0m"
}

print_exception() {
    local num=$1
    local line="$2"
    echo -e "\033[1;31mException $num:\033[0m"
    echo -e "\033[0;33m$line\033[0m"
    echo ""
}

print_info() {
    echo -e "\033[1;32m$1\033[0m"
}

print_warning() {
    echo -e "\033[1;33m$1\033[0m"
}

print_error() {
    echo -e "\033[1;31m$1\033[0m"
}

# Check if log file is provided
if [ $# -eq 0 ]; then
    print_error "Error: No log file specified"
    echo ""
    usage
fi

LOG_FILE="$1"

# Check if log file exists
if [ ! -f "$LOG_FILE" ]; then
    print_error "Error: Log file '$LOG_FILE' not found"
    exit 1
fi

# Check if log file is readable
if [ ! -r "$LOG_FILE" ]; then
    print_error "Error: Log file '$LOG_FILE' is not readable"
    exit 1
fi

print_header "QEMU Exception Extractor"
print_info "Analyzing log file: $LOG_FILE"
echo ""

# Count total exceptions
total_exceptions=$(grep -c "^do_raise_exception:" "$LOG_FILE" 2>/dev/null || echo "0")

if [ "$total_exceptions" -eq 0 ]; then
    print_warning "No exceptions found in log file"
    print_info "Searched for lines starting with 'do_raise_exception:'"
    exit 0
fi

print_info "Total exceptions found: $total_exceptions"
echo ""

# Extract first 3 exceptions
print_header "First 3 Exceptions"

exception_count=0
while IFS= read -r line; do
    if [[ $line == do_raise_exception:* ]]; then
        exception_count=$((exception_count + 1))
        print_exception "$exception_count" "$line"
        
        # Stop after 3 exceptions
        if [ "$exception_count" -eq 3 ]; then
            break
        fi
    fi
done < "$LOG_FILE"

# Summary
print_header "Summary"
if [ "$exception_count" -lt 3 ] && [ "$total_exceptions" -gt 0 ]; then
    print_info "Displayed all $exception_count exception(s) found"
else
    print_info "Displayed first 3 exceptions out of $total_exceptions total"
fi

# Additional analysis
print_header "Exception Analysis"

# Count exception types
echo "Exception type breakdown:"
grep "^do_raise_exception:" "$LOG_FILE" | sed 's/.*exception: \([0-9]*\).*/\1/' | sort | uniq -c | while read count type; do
    echo "  Exception type $type: $count occurrences"
done

echo ""

# Show unique exception messages
echo "Unique exception descriptions:"
grep "^do_raise_exception:" "$LOG_FILE" | sed 's/.*(\([^)]*\)).*/\1/' | sort | uniq | while read desc; do
    echo "  - $desc"
done

echo ""
print_info "Analysis complete!"
