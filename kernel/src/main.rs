#![no_std]
#![no_main]

use core::panic::PanicInfo;

// Import boot library to ensure kernel_init is linked
extern crate boot;

// Import console for I/O
use console::println;


#[no_mangle]
pub extern "C" fn rust_main() -> ! {
    // Main kernel function

    // Initialize kernel subsystems
    init_kernel();
    
    // Main kernel loop
    loop {
        // Kernel main loop - handle interrupts, scheduling, etc.
    }
}


/// Initialize kernel subsystems
///
/// This function is called from rust_main() to initialize various kernel
/// subsystems after the architecture-specific initialization is complete.
fn init_kernel() {
    // Initialize console first
    console::init();

    // Initialize memory allocator
    init_allocator();

    // Initialize device drivers
    init_drivers();

    // Initialize filesystem
    init_filesystem();

    // Initialize process management
    init_process_management();

    // Initialize networking (if available)
    init_networking();

    // Print kernel startup message
    print_startup_message();
}

/// Initialize memory allocator
fn init_allocator() {
    // TODO: Initialize heap allocator
    // This will be implemented when we add memory management
}

/// Initialize device drivers
fn init_drivers() {
    // TODO: Initialize device drivers
    // This will include UART, timer, interrupt controller, etc.
}

/// Initialize filesystem
fn init_filesystem() {
    // TODO: Initialize virtual filesystem
    // This will be implemented when we add filesystem support
}

/// Initialize process management
fn init_process_management() {
    // TODO: Initialize scheduler and process management
    // This will be implemented when we add process support
}

/// Initialize networking
fn init_networking() {
    // TODO: Initialize network stack
    // This will be implemented when we add networking support
}

/// Print kernel startup message
fn print_startup_message() {
    println!("=== Echos OS v0.1.0 ===");
    #[cfg(target_arch = "riscv64")]
    println!("Architecture: RISC-V 64-bit");
    #[cfg(target_arch = "loongarch64")]
    println!("Architecture: LoongArch 64-bit");
    println!("Kernel initialization complete");
    println!("Ready to serve!");

    // Demonstrate shutdown functionality
    println!("Testing shutdown in 3 seconds...");

    // Simple delay (not accurate, just for demonstration)
    for i in (1..=3).rev() {
        println!("Shutdown in {}...", i);
        for _ in 0..1000000 {
            core::hint::spin_loop();
        }
    }

    // Call architecture-specific shutdown
    arch::power::shutdown();
}

#[panic_handler]
fn panic(_info: &PanicInfo) -> ! {
    loop {}
}
