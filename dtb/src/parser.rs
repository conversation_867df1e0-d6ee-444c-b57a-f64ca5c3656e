//! DTB Parser using fdt-parser library
//!
//! This module provides DTB parsing functionality using the fdt-parser crate
//! and stores parsed information in alloc-based data structures.

extern crate alloc;
use alloc::{string::{String, ToString}, vec::Vec};
use log::*;

/// Main DTB information container
#[derive(Debug, Clone)]
pub struct DtbInfo {
    /// DTB header information
    pub header: DtbHeader,
    /// Memory regions found in DTB
    pub memory_regions: Vec<DtbMemInfoItem>,
    /// CPU information
    pub cpus: Vec<CpuInfo>,
    /// Device information
    pub devices: Vec<DeviceInfoItem>,
    /// Interrupt controller information
    pub interrupt_controllers: Vec<InterruptController>,
}

/// DTB header information
#[derive(Debug, <PERSON>lone, Default)]
pub struct DtbHeader {
    /// Magic number (should be 0xd00dfeed)
    pub magic: u32,
    /// Total size of DTB
    pub total_size: u32,
    /// Offset to structure block
    pub off_dt_struct: u32,
    /// Offset to strings block
    pub off_dt_strings: u32,
    /// Offset to memory reservation map
    pub off_mem_rsvmap: u32,
    /// Version of DTB format
    pub version: u32,
    /// Last compatible version
    pub last_comp_version: u32,
}

/// Memory region information
#[derive(Debug, Clone)]
pub struct DtbMemInfoItem {
    /// Base physical address
    pub base: usize,
    /// Size in bytes
    pub size: usize,
    /// Memory type (if specified)
    pub mem_type: Option<String>,
}

/// CPU information
#[derive(Debug, Clone)]
pub struct CpuInfo {
    /// CPU/Hart ID
    pub cpu_id: usize,
    /// ISA string (e.g., "rv64imafdch", "loongarch64")
    pub isa: Option<String>,
    /// MMU type (e.g., "sv39", "sv48", "loongarch64")
    pub mmu_type: Option<String>,
    /// Clock frequency
    pub clock_frequency: Option<u64>,
    /// Cache information
    pub cache_info: Option<CacheInfo>,
}

/// Cache information
#[derive(Debug, Clone)]
pub struct CacheInfo {
    /// L1 instruction cache size
    pub l1i_cache_size: Option<u32>,
    /// L1 data cache size
    pub l1d_cache_size: Option<u32>,
    /// L2 cache size
    pub l2_cache_size: Option<u32>,
    /// Cache line size
    pub cache_line_size: Option<u32>,
}

/// Device information
#[derive(Debug, Clone)]
pub struct DeviceInfoItem {
    /// Device name
    pub name: String,
    /// Compatible strings
    pub compatible: Vec<String>,
    /// Memory regions used by this device
    pub regions: Vec<DtbMemInfoItem>,
    /// Interrupt information
    pub interrupts: Vec<u32>,
    /// Device-specific properties
    pub properties: Vec<DeviceProperty>,
}

/// Device property
#[derive(Debug, Clone)]
pub struct DeviceProperty {
    /// Property name
    pub name: String,
    /// Property value (as raw bytes)
    pub value: Vec<u8>,
}

/// Interrupt controller information
#[derive(Debug, Clone)]
pub struct InterruptController {
    /// Controller name
    pub name: String,
    /// Compatible strings
    pub compatible: Vec<String>,
    /// Base address
    pub base_address: usize,
    /// Size of register region
    pub size: usize,
    /// Number of interrupt lines
    pub interrupt_cells: Option<u32>,
    /// Controller type
    pub controller_type: InterruptControllerType,
}

/// Types of interrupt controllers
#[derive(Debug, Clone)]
pub enum InterruptControllerType {
    /// RISC-V Platform-Level Interrupt Controller
    RiscvPlic,
    /// RISC-V Core Local Interruptor
    RiscvClint,
    /// ARM Generic Interrupt Controller
    ArmGic,
    /// LoongArch interrupt controllers
    LoongArchPchPic,
    LoongArchPchMsi,
    LoongArchIpi,
    /// Unknown/Other
    Other(String),
}

/// DTB parsing errors
#[derive(Debug, Clone)]
pub enum DtbError {
    /// Invalid DTB address
    InvalidAddress,
    /// Invalid DTB size
    InvalidSize,
    /// Invalid DTB magic number
    InvalidMagic,
    /// FDT parsing error
    FdtError(String),
    /// Parsing error
    ParseError(String),
}

/// DTB Parser using fdt-parser library
pub struct DtbParser {
    /// Raw DTB data
    dtb_data: Vec<u8>,
    /// Parsed DTB information
    dtb_info: DtbInfo,
    /// Parser state
    is_parsed: bool,
}

impl DtbInfo {
    /// Create a new empty DTB info structure
    pub fn new() -> Self {
        Self {
            header: DtbHeader::default(),
            memory_regions: Vec::new(),
            cpus: Vec::new(),
            devices: Vec::new(),
            interrupt_controllers: Vec::new(),
        }
    }

    /// Get total memory size
    pub fn total_memory_size(&self) -> usize {
        self.memory_regions.iter().map(|region| region.size).sum()
    }

    /// Get number of CPUs
    pub fn cpu_count(&self) -> usize {
        self.cpus.len()
    }

    /// Find device by compatible string
    pub fn find_device_by_compatible(&self, compatible: &str) -> Option<&DeviceInfoItem> {
        self.devices.iter().find(|device| {
            device.compatible.iter().any(|c| c == compatible)
        })
    }

    /// Find interrupt controller by type
    pub fn find_interrupt_controller(&self, controller_type: &InterruptControllerType) -> Option<&InterruptController> {
        self.interrupt_controllers.iter().find(|ic| {
            core::mem::discriminant(&ic.controller_type) == core::mem::discriminant(controller_type)
        })
    }

    /// Display DTB information using DTB logging
    pub fn display(&self) {
        dtb_info!("=== DTB Information ===");

        // Display header information
        dtb_info!("DTB Header:");
        dtb_info!("  Magic: 0x{:08x}", self.header.magic);
        dtb_info!("  Total size: {} bytes", self.header.total_size);
        dtb_info!("  Version: {}", self.header.version);

        // Display memory information
        dtb_info!("Memory regions: {}", self.memory_regions.len());
        for (i, region) in self.memory_regions.iter().enumerate() {
            dtb_info!("  Memory {}: 0x{:x} - 0x{:x} ({} MB)",
                     i, region.base, region.end_address(), region.size / (1024 * 1024));
        }

        // Display CPU information
        dtb_info!("CPUs: {}", self.cpus.len());
        for (i, cpu) in self.cpus.iter().enumerate() {
            dtb_info!("  CPU {}: id={}, isa={:?}, mmu={:?}",
                     i, cpu.cpu_id, cpu.isa, cpu.mmu_type);
            if let Some(freq) = cpu.clock_frequency {
                dtb_info!("    Clock frequency: {} Hz", freq);
            }
        }

        // Display device information
        dtb_info!("Devices: {}", self.devices.len());
        for device in &self.devices {
            dtb_info!("  Device: {} (compatible: {:?})", device.name, device.compatible);
            for region in &device.regions {
                dtb_info!("    Region: 0x{:x} - 0x{:x} ({} bytes)",
                         region.base, region.end_address(), region.size);
            }
            if !device.interrupts.is_empty() {
                dtb_info!("    Interrupts: {:?}", device.interrupts);
            }
        }

        dtb_info!("Total memory: {} MB", self.total_memory_size() / (1024 * 1024));
        dtb_info!("=== End DTB Information ===");
    }
}

impl DtbMemInfoItem {
    /// Create a new memory region
    pub fn new(base: usize, size: usize) -> Self {
        Self {
            base,
            size,
            mem_type: None,
        }
    }

    /// Get end address of this memory region
    pub fn end_address(&self) -> usize {
        self.base + self.size
    }

    /// Check if an address is within this memory region
    pub fn contains_address(&self, addr: usize) -> bool {
        addr >= self.base && addr < self.end_address()
    }
}

impl DeviceInfoItem {
    /// Create a new device info
    pub fn new(name: String) -> Self {
        Self {
            name,
            compatible: Vec::new(),
            regions: Vec::new(),
            interrupts: Vec::new(),
            properties: Vec::new(),
        }
    }

    /// Add a compatible string
    pub fn add_compatible(&mut self, compatible: String) {
        self.compatible.push(compatible);
    }

    /// Add a memory region
    pub fn add_region(&mut self, region: DtbMemInfoItem) {
        self.regions.push(region);
    }

    /// Get property by name
    pub fn get_property(&self, name: &str) -> Option<&DeviceProperty> {
        self.properties.iter().find(|prop| prop.name == name)
    }
}

impl InterruptController {
    /// Create a new interrupt controller
    pub fn new(name: String, controller_type: InterruptControllerType) -> Self {
        Self {
            name,
            compatible: Vec::new(),
            base_address: 0,
            size: 0,
            interrupt_cells: None,
            controller_type,
        }
    }
}

impl DtbParser {
    /// Create a new DTB parser from raw data
    pub fn new(dtb_data: Vec<u8>) -> Self {
        Self {
            dtb_data,
            dtb_info: DtbInfo::new(),
            is_parsed: false,
        }
    }

    /// Create a DTB parser from a memory address and size
    pub unsafe fn from_memory(addr: usize, size: usize) -> Result<Self, DtbError> {
        if addr == 0 || size == 0 {
            return Err(DtbError::InvalidAddress);
        }

        // Copy DTB data from memory
        let slice = core::slice::from_raw_parts(addr as *const u8, size);
        let dtb_data = Vec::from(slice);

        // Verify DTB magic
        if dtb_data.len() < 4 {
            return Err(DtbError::InvalidSize);
        }

        let magic = u32::from_be_bytes([dtb_data[0], dtb_data[1], dtb_data[2], dtb_data[3]]);

        if magic != 0xd00dfeed {
            return Err(DtbError::InvalidMagic);
        }

        Ok(Self::new(dtb_data))
    }

    /// Parse the DTB using fdt-parser
    pub fn parse(&mut self) -> Result<(), DtbError> {
        if self.is_parsed {
            return Ok(());
        }

        // Clone the data to avoid borrowing issues
        let dtb_data = self.dtb_data.clone();

        // Parse using fdt-parser
        match fdt_parser::Fdt::from_bytes(&dtb_data) {
            Ok(fdt) => {
                self.parse_with_fdt(&fdt)?;
                self.is_parsed = true;
                Ok(())
            }
            Err(_e) => {
                Err(DtbError::FdtError("FDT parsing failed".to_string()))
            }
        }
    }

    /// Get the parsed DTB information
    pub fn get_info(&self) -> &DtbInfo {
        &self.dtb_info
    }

    /// Parse DTB using fdt-parser library
    fn parse_with_fdt(&mut self, fdt: &fdt_parser::Fdt) -> Result<(), DtbError> {
        // Parse header information
        self.parse_header(fdt)?;

        // Parse memory nodes
        self.parse_memory_nodes(fdt)?;

        // Parse CPU nodes
        self.parse_cpu_nodes(fdt)?;

        // Parse device nodes
        self.parse_device_nodes(fdt)?;

        Ok(())
    }

    /// Parse DTB header information
    fn parse_header(&mut self, _fdt: &fdt_parser::Fdt) -> Result<(), DtbError> {
        // Extract header information from the raw DTB data
        if self.dtb_data.len() < 28 {
            return Err(DtbError::InvalidSize);
        }

        self.dtb_info.header.magic = u32::from_be_bytes([
            self.dtb_data[0], self.dtb_data[1], self.dtb_data[2], self.dtb_data[3]
        ]);
        self.dtb_info.header.total_size = u32::from_be_bytes([
            self.dtb_data[4], self.dtb_data[5], self.dtb_data[6], self.dtb_data[7]
        ]);
        self.dtb_info.header.off_dt_struct = u32::from_be_bytes([
            self.dtb_data[8], self.dtb_data[9], self.dtb_data[10], self.dtb_data[11]
        ]);
        self.dtb_info.header.off_dt_strings = u32::from_be_bytes([
            self.dtb_data[12], self.dtb_data[13], self.dtb_data[14], self.dtb_data[15]
        ]);
        self.dtb_info.header.off_mem_rsvmap = u32::from_be_bytes([
            self.dtb_data[16], self.dtb_data[17], self.dtb_data[18], self.dtb_data[19]
        ]);
        self.dtb_info.header.version = u32::from_be_bytes([
            self.dtb_data[20], self.dtb_data[21], self.dtb_data[22], self.dtb_data[23]
        ]);
        self.dtb_info.header.last_comp_version = u32::from_be_bytes([
            self.dtb_data[24], self.dtb_data[25], self.dtb_data[26], self.dtb_data[27]
        ]);

        Ok(())
    }

    /// Parse memory nodes
    fn parse_memory_nodes(&mut self, fdt: &fdt_parser::Fdt) -> Result<(), DtbError> {
        for node in fdt.all_nodes() {
            let name = node.name();
            if name.starts_with("memory") {
                if let Some(reg) = node.reg() {
                    for reg_item in reg {
                        let addr = reg_item.address as usize;
                        let size = reg_item.size.unwrap_or(0) as usize;
                        let memory_region = DtbMemInfoItem::new(addr, size);
                        self.dtb_info.memory_regions.push(memory_region);
                    }
                }
            }
        }

        // If no memory nodes found, add a default one
        if self.dtb_info.memory_regions.is_empty() {
            let default_memory = DtbMemInfoItem::new(0x80000000, 0x40000000); // 1GB at 2GB
            self.dtb_info.memory_regions.push(default_memory);
        }

        Ok(())
    }

    /// Parse CPU nodes
    fn parse_cpu_nodes(&mut self, fdt: &fdt_parser::Fdt) -> Result<(), DtbError> {
        for node in fdt.all_nodes() {
            let name = node.name();
            if name.starts_with("cpu") && name != "cpus" {
                let mut cpu = CpuInfo {
                    cpu_id: self.dtb_info.cpus.len(),
                    isa: None,
                    mmu_type: None,
                    clock_frequency: None,
                    cache_info: None,
                };

                // Try to get ISA information
                for prop in node.propertys() {
                    let prop_name = prop.name;
                    if prop_name == "riscv,isa" {
                        if let Ok(isa_str) = core::str::from_utf8(prop.raw_value()) {
                            cpu.isa = Some(isa_str.trim_end_matches('\0').to_string());
                        }
                    } else if prop_name == "mmu-type" {
                        if let Ok(mmu_str) = core::str::from_utf8(prop.raw_value()) {
                            cpu.mmu_type = Some(mmu_str.trim_end_matches('\0').to_string());
                        }
                    } else if prop_name == "clock-frequency" {
                        let value = prop.raw_value();
                        if value.len() >= 4 {
                            let freq = u32::from_be_bytes([
                                value[0], value[1], value[2], value[3]
                            ]);
                            cpu.clock_frequency = Some(freq as u64);
                        }
                    }
                }

                self.dtb_info.cpus.push(cpu);
            }
        }

        // If no CPU nodes found, add a default one
        if self.dtb_info.cpus.is_empty() {
            let default_cpu = CpuInfo {
                cpu_id: 0,
                isa: Some("rv64imafdch".to_string()),
                mmu_type: Some("sv39".to_string()),
                clock_frequency: None,
                cache_info: None,
            };
            self.dtb_info.cpus.push(default_cpu);
        }

        Ok(())
    }

    /// Parse device nodes
    fn parse_device_nodes(&mut self, fdt: &fdt_parser::Fdt) -> Result<(), DtbError> {
        for node in fdt.all_nodes() {
            let name = node.name();
            // Skip root, memory, and CPU nodes
            if name == "/" || name.starts_with("memory") || name.starts_with("cpu") || name == "cpus" {
                continue;
            }

            let mut device = DeviceInfoItem::new(name.to_string());

            // Get properties
            for prop in node.propertys() {
                let prop_name = prop.name;
                if prop_name == "compatible" {
                    let compat_str = core::str::from_utf8(prop.raw_value()).unwrap_or("");
                    for compat in compat_str.split('\0').filter(|s| !s.is_empty()) {
                        device.add_compatible(compat.to_string());
                    }
                } else if prop_name == "interrupts" {
                    let value = prop.raw_value();
                    let mut i = 0;
                    while i + 3 < value.len() {
                        let interrupt = u32::from_be_bytes([
                            value[i], value[i+1], value[i+2], value[i+3]
                        ]);
                        device.interrupts.push(interrupt);
                        i += 4;
                    }
                }
            }

            // Get register information
            if let Some(reg) = node.reg() {
                for reg_item in reg {
                    let addr = reg_item.address as usize;
                    let size = reg_item.size.unwrap_or(0) as usize;
                    device.add_region(DtbMemInfoItem::new(addr, size));
                }
            }

            if !device.compatible.is_empty() || !device.regions.is_empty() {
                self.dtb_info.devices.push(device);
            }
        }

        Ok(())
    }
}