#![no_std]

//! Device Tree Blob (DTB) parsing module for Echos OS
//!
//! This module provides functionality to parse DTB files and extract
//! hardware information such as memory layout, CPU details, and device configurations.

extern crate alloc;

use log::*;

pub mod parser;

// Re-export commonly used types
pub use parser::{
    DtbInfo, DtbHeader, DtbMemInfoItem, CpuInfo, DeviceInfoItem,
    InterruptController, InterruptControllerType
};

/// Initialize DTB module (ensures heap is available)
pub fn init_dtb_module() {
    dtb_info!("DTB module initialized with alloc support");
}