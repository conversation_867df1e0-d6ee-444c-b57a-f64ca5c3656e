/dts-v1/;

/ {
	#size-cells = <0x02>;
	#address-cells = <0x02>;
	compatible = "linux,dummy-loongson3";

	platform-bus@16000000 {
		interrupt-parent = <0x8003>;
		ranges = <0x00 0x00 0x16000000 0x2000000>;
		#address-cells = <0x01>;
		#size-cells = <0x01>;
		compatible = "qemu,platform\0simple-bus";
	};

	poweroff {
		value = <0x34>;
		offset = <0x00>;
		regmap = <0x8005>;
		compatible = "syscon-poweroff";
	};

	reboot {
		value = <0x42>;
		offset = <0x02>;
		regmap = <0x8005>;
		compatible = "syscon-reboot";
	};

	ged@100e001c {
		phandle = <0x8005>;
		reg-io-width = <0x01>;
		reg-shift = <0x00>;
		reg = <0x00 0x100e001c 0x00 0x03>;
		compatible = "syscon";
	};

	rtc@100d0100 {
		interrupt-parent = <0x8003>;
		interrupts = <0x06 0x04>;
		reg = <0x00 0x100d0100 0x00 0x100>;
		compatible = "loongson,ls7a-rtc";
	};

	serial@1fe001e0 {
		interrupt-parent = <0x8003>;
		interrupts = <0x02 0x04>;
		clock-frequency = <0x5f5e100>;
		reg = <0x00 0x1fe001e0 0x00 0x100>;
		compatible = "ns16550a";
	};

	serial@1fe002e0 {
		interrupt-parent = <0x8003>;
		interrupts = <0x03 0x04>;
		clock-frequency = <0x5f5e100>;
		reg = <0x00 0x1fe002e0 0x00 0x100>;
		compatible = "ns16550a";
	};

	serial@1fe003e0 {
		interrupt-parent = <0x8003>;
		interrupts = <0x04 0x04>;
		clock-frequency = <0x5f5e100>;
		reg = <0x00 0x1fe003e0 0x00 0x100>;
		compatible = "ns16550a";
	};

	serial@1fe004e0 {
		interrupt-parent = <0x8003>;
		interrupts = <0x05 0x04>;
		clock-frequency = <0x5f5e100>;
		reg = <0x00 0x1fe004e0 0x00 0x100>;
		compatible = "ns16550a";
	};

	pcie@20000000 {
		interrupt-map-mask = <0x1800 0x00 0x00 0x07>;
		interrupt-map = <0x00 0x00 0x00 0x01 0x8003 0x10 0x00 0x00 0x00 0x02 0x8003 0x11 0x00 0x00 0x00 0x03 0x8003 0x12 0x00 0x00 0x00 0x04 0x8003 0x13 0x800 0x00 0x00 0x01 0x8003 0x11 0x800 0x00 0x00 0x02 0x8003 0x12 0x800 0x00 0x00 0x03 0x8003 0x13 0x800 0x00 0x00 0x04 0x8003 0x10 0x1000 0x00 0x00 0x01 0x8003 0x12 0x1000 0x00 0x00 0x02 0x8003 0x13 0x1000 0x00 0x00 0x03 0x8003 0x10 0x1000 0x00 0x00 0x04 0x8003 0x11 0x1800 0x00 0x00 0x01 0x8003 0x13 0x1800 0x00 0x00 0x02 0x8003 0x10 0x1800 0x00 0x00 0x03 0x8003 0x11 0x1800 0x00 0x00 0x04 0x8003 0x12>;
		msi-map = <0x00 0x8004 0x00 0x10000>;
		ranges = <0x1000000 0x00 0x4000 0x00 0x18004000 0x00 0xc000 0x2000000 0x00 0x40000000 0x00 0x40000000 0x00 0x40000000>;
		reg = <0x00 0x20000000 0x00 0x8000000>;
		dma-coherent;
		bus-range = <0x00 0x7f>;
		linux,pci-domain = <0x00>;
		#size-cells = <0x02>;
		#address-cells = <0x03>;
		device_type = "pci";
		compatible = "pci-host-ecam-generic";
	};

	msi@2ff00000 {
		loongson,msi-num-vecs = <0xe0>;
		loongson,msi-base-vec = <0x20>;
		interrupt-parent = <0x8002>;
		interrupt-controller;
		reg = <0x00 0x2ff00000 0x00 0x08>;
		compatible = "loongson,pch-msi-1.0";
		phandle = <0x8004>;
	};

	platic@10000000 {
		loongson,pic-base-vec = <0x00>;
		interrupt-parent = <0x8002>;
		#interrupt-cells = <0x02>;
		interrupt-controller;
		reg = <0x00 0x10000000 0x00 0x400>;
		compatible = "loongson,pch-pic-1.0";
		phandle = <0x8003>;
	};

	eiointc@1400 {
		reg = <0x00 0x1400 0x00 0x800>;
		interrupts = <0x03>;
		interrupt-parent = <0x8001>;
		#interrupt-cells = <0x01>;
		interrupt-controller;
		compatible = "loongson,ls2k2000-eiointc";
		phandle = <0x8002>;
	};

	cpuic {
		#interrupt-cells = <0x01>;
		interrupt-controller;
		compatible = "loongson,cpu-interrupt-controller";
		phandle = <0x8001>;
	};

	flash@1c000000 {
		bank-width = <0x04>;
		reg = <0x00 0x1c000000 0x00 0x1000000 0x00 0x1d000000 0x00 0x1000000>;
		compatible = "cfi-flash";
	};

	fw_cfg@1e020000 {
		dma-coherent;
		reg = <0x00 0x1e020000 0x00 0x18>;
		compatible = "qemu,fw-cfg-mmio";
	};

	memory@0 {
		device_type = "memory";
		reg = <0x00 0x00 0x00 0x8000000>;
	};

	cpus {
		#size-cells = <0x00>;
		#address-cells = <0x01>;

		cpu-map {

			socket0 {

				core0 {
					cpu = <0x8000>;
				};
			};
		};

		cpu@0 {
			phandle = <0x8000>;
			reg = <0x00>;
			compatible = "loongarch,Loongson-3A5000";
			device_type = "cpu";
		};
	};

	chosen {
		stdout-path = "/serial@1fe001e0";
		rng-seed = <0xc587d8f2 0x5cc0333 0xec74df8d 0x5b3eb32f 0xc22c33fc 0x3e4ee867 0x7dcab5f8 0xf26be758>;
	};
};
