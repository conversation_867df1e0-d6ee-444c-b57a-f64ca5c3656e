/dts-v1/;

/ {
	#address-cells = <0x02>;
	#size-cells = <0x02>;
	compatible = "riscv-virtio";
	model = "riscv-virtio,qemu";

	poweroff {
		value = <0x5555>;
		offset = <0x00>;
		regmap = <0x04>;
		compatible = "syscon-poweroff";
	};

	reboot {
		value = <0x7777>;
		offset = <0x00>;
		regmap = <0x04>;
		compatible = "syscon-reboot";
	};

	platform-bus@4000000 {
		interrupt-parent = <0x03>;
		ranges = <0x00 0x00 0x4000000 0x2000000>;
		#address-cells = <0x01>;
		#size-cells = <0x01>;
		compatible = "qemu,platform\0simple-bus";
	};

	memory@80000000 {
		device_type = "memory";
		reg = <0x00 0x80000000 0x00 0x8000000>;
	};

	cpus {
		#address-cells = <0x01>;
		#size-cells = <0x00>;
		timebase-frequency = <0x989680>;

		cpu@0 {
			phandle = <0x01>;
			device_type = "cpu";
			reg = <0x00>;
			status = "okay";
			compatible = "riscv";
			riscv,cbop-block-size = <0x40>;
			riscv,cboz-block-size = <0x40>;
			riscv,cbom-block-size = <0x40>;
			riscv,isa-extensions = "i\0m\0a\0f\0d\0c\0h\0zic64b\0zicbom\0zicbop\0zicboz\0ziccamoa\0ziccif\0zicclsm\0ziccrse\0zicntr\0zicsr\0zifencei\0zihintntl\0zihintpause\0zihpm\0zmmul\0za64rs\0zaamo\0zalrsc\0zawrs\0zfa\0zca\0zcd\0zba\0zbb\0zbc\0zbs\0ssccptr\0sscounterenw\0sstc\0sstvala\0sstvecd\0svadu\0svvptc";
			riscv,isa-base = "rv64i";
			riscv,isa = "rv64imafdch_zic64b_zicbom_zicbop_zicboz_ziccamoa_ziccif_zicclsm_ziccrse_zicntr_zicsr_zifencei_zihintntl_zihintpause_zihpm_zmmul_za64rs_zaamo_zalrsc_zawrs_zfa_zca_zcd_zba_zbb_zbc_zbs_ssccptr_sscounterenw_sstc_sstvala_sstvecd_svadu_svvptc";
			mmu-type = "riscv,sv57";

			interrupt-controller {
				#interrupt-cells = <0x01>;
				interrupt-controller;
				compatible = "riscv,cpu-intc";
				phandle = <0x02>;
			};
		};

		cpu-map {

			cluster0 {

				core0 {
					cpu = <0x01>;
				};
			};
		};
	};

	pmu {
		riscv,event-to-mhpmcounters = <0x01 0x01 0x7fff9 0x02 0x02 0x7fffc 0x10019 0x10019 0x7fff8 0x1001b 0x1001b 0x7fff8 0x10021 0x10021 0x7fff8>;
		compatible = "riscv,pmu";
	};

	fw-cfg@******** {
		dma-coherent;
		reg = <0x00 0x******** 0x00 0x18>;
		compatible = "qemu,fw-cfg-mmio";
	};

	flash@******** {
		bank-width = <0x04>;
		reg = <0x00 0x******** 0x00 0x2000000 0x00 0x22000000 0x00 0x2000000>;
		compatible = "cfi-flash";
	};

	chosen {
		stdout-path = "/soc/serial@********";
		rng-seed = <0xac66350 0xb300426a 0x1622f769 0xc9cff9a1 0xc4e61abd 0xea841dca 0xb0b525b8 0x9a39c06d>;
	};

	soc {
		#address-cells = <0x02>;
		#size-cells = <0x02>;
		compatible = "simple-bus";
		ranges;

		rtc@101000 {
			interrupts = <0x0b>;
			interrupt-parent = <0x03>;
			reg = <0x00 0x101000 0x00 0x1000>;
			compatible = "google,goldfish-rtc";
		};

		serial@******** {
			interrupts = <0x0a>;
			interrupt-parent = <0x03>;
			clock-frequency = "\08@";
			reg = <0x00 0x******** 0x00 0x100>;
			compatible = "ns16550a";
		};

		test@100000 {
			phandle = <0x04>;
			reg = <0x00 0x100000 0x00 0x1000>;
			compatible = "sifive,test1\0sifive,test0\0syscon";
		};

		virtio_mmio@******** {
			interrupts = <0x08>;
			interrupt-parent = <0x03>;
			reg = <0x00 0x******** 0x00 0x1000>;
			compatible = "virtio,mmio";
		};

		virtio_mmio@10007000 {
			interrupts = <0x07>;
			interrupt-parent = <0x03>;
			reg = <0x00 0x10007000 0x00 0x1000>;
			compatible = "virtio,mmio";
		};

		virtio_mmio@10006000 {
			interrupts = <0x06>;
			interrupt-parent = <0x03>;
			reg = <0x00 0x10006000 0x00 0x1000>;
			compatible = "virtio,mmio";
		};

		virtio_mmio@10005000 {
			interrupts = <0x05>;
			interrupt-parent = <0x03>;
			reg = <0x00 0x10005000 0x00 0x1000>;
			compatible = "virtio,mmio";
		};

		virtio_mmio@10004000 {
			interrupts = <0x04>;
			interrupt-parent = <0x03>;
			reg = <0x00 0x10004000 0x00 0x1000>;
			compatible = "virtio,mmio";
		};

		virtio_mmio@10003000 {
			interrupts = <0x03>;
			interrupt-parent = <0x03>;
			reg = <0x00 0x10003000 0x00 0x1000>;
			compatible = "virtio,mmio";
		};

		virtio_mmio@10002000 {
			interrupts = <0x02>;
			interrupt-parent = <0x03>;
			reg = <0x00 0x10002000 0x00 0x1000>;
			compatible = "virtio,mmio";
		};

		virtio_mmio@10001000 {
			interrupts = <0x01>;
			interrupt-parent = <0x03>;
			reg = <0x00 0x10001000 0x00 0x1000>;
			compatible = "virtio,mmio";
		};

		plic@c000000 {
			phandle = <0x03>;
			riscv,ndev = <0x5f>;
			reg = <0x00 0xc000000 0x00 0x600000>;
			interrupts-extended = <0x02 0x0b 0x02 0x09>;
			interrupt-controller;
			compatible = "sifive,plic-1.0.0\0riscv,plic0";
			#address-cells = <0x00>;
			#interrupt-cells = <0x01>;
		};

		clint@2000000 {
			interrupts-extended = <0x02 0x03 0x02 0x07>;
			reg = <0x00 0x2000000 0x00 0x10000>;
			compatible = "sifive,clint0\0riscv,clint0";
		};

		pci@30000000 {
			interrupt-map-mask = <0x1800 0x00 0x00 0x07>;
			interrupt-map = <0x00 0x00 0x00 0x01 0x03 0x20 0x00 0x00 0x00 0x02 0x03 0x21 0x00 0x00 0x00 0x03 0x03 0x22 0x00 0x00 0x00 0x04 0x03 0x23 0x800 0x00 0x00 0x01 0x03 0x21 0x800 0x00 0x00 0x02 0x03 0x22 0x800 0x00 0x00 0x03 0x03 0x23 0x800 0x00 0x00 0x04 0x03 0x20 0x1000 0x00 0x00 0x01 0x03 0x22 0x1000 0x00 0x00 0x02 0x03 0x23 0x1000 0x00 0x00 0x03 0x03 0x20 0x1000 0x00 0x00 0x04 0x03 0x21 0x1800 0x00 0x00 0x01 0x03 0x23 0x1800 0x00 0x00 0x02 0x03 0x20 0x1800 0x00 0x00 0x03 0x03 0x21 0x1800 0x00 0x00 0x04 0x03 0x22>;
			ranges = <0x1000000 0x00 0x00 0x00 0x3000000 0x00 0x10000 0x2000000 0x00 0x40000000 0x00 0x40000000 0x00 0x40000000 0x3000000 0x04 0x00 0x04 0x00 0x04 0x00>;
			reg = <0x00 0x30000000 0x00 0x********>;
			dma-coherent;
			bus-range = <0x00 0xff>;
			linux,pci-domain = <0x00>;
			device_type = "pci";
			compatible = "pci-host-ecam-generic";
			#size-cells = <0x02>;
			#interrupt-cells = <0x01>;
			#address-cells = <0x03>;
		};
	};
};
