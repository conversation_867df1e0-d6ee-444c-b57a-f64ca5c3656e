#![no_std]

//! Memory management module for Echos OS
//!
//! This module provides heap allocation and memory management functionality.

extern crate alloc;

// Core heap implementation
pub mod heap;

// Address and page abstractions
pub mod address;

// Physical frame allocator
pub mod frame;

// Kernel memory layout
pub mod layout;

// Test modules
pub mod tests;

// Re-export commonly used items
pub use heap::{
    init_heap,
    heap_stats,
    print_heap_stats,
    get_heap_size,
    is_heap_initialized,
    HeapStats
};

pub use address::{
    PhysAddr, VirtAddr, PhysPageNum, VirtPageNum,
    PAGE_SIZE, PAGE_SIZE_SHIFT
};

pub use frame::{
    FrameAllocator, MemorySegment, SegmentedFrameAllocator,
    init_frame_allocator, alloc_frame, alloc_frames,
    dealloc_frame, dealloc_frames, print_frame_stats
};

pub use layout::{
    kernel_start_virt, kernel_end_virt, kernel_start_phys, kernel_end_phys,
    virt_to_phys, phys_to_virt, overlaps_with_kernel, kernel_layout, KernelLayout
};

pub use tests::{test_heap, quick_test, test_address, test_frame_allocator};

/// Example function demonstrating address and frame usage
pub fn demo_memory_management() {
    use crate::address::*;
    use crate::frame::*;
    use log::*;

    heap_info!("=== Memory Management Demo ===");

    // Test address operations
    heap_info!("Testing address operations:");
    let paddr = PhysAddr::new(0x100000);
    let vaddr = phys_to_virt(paddr);
    heap_info!("  Physical address: {}", paddr);
    heap_info!("  Virtual address: {}", vaddr);
    heap_info!("  Page number: {}", paddr.page_number());
    heap_info!("  Page offset: 0x{:x}", paddr.page_offset());

    // Test frame allocator setup
    heap_info!("Setting up frame allocator:");
    let segments = alloc::vec![
        MemorySegment::new("low_memory", PhysAddr::new(0x100000), PhysAddr::new(0x200000), true),
        MemorySegment::new("high_memory", PhysAddr::new(0x400000), PhysAddr::new(0x500000), true),
        MemorySegment::new("reserved", PhysAddr::new(0x300000), PhysAddr::new(0x400000), false),
    ];

    init_frame_allocator(segments);

    // Test frame allocation
    heap_info!("Testing frame allocation:");
    if let Some(frame1) = alloc_frame() {
        heap_info!("  Allocated frame: {}", frame1);
        heap_info!("  Frame address: {}", frame1.start_address());

        if let Some(frames) = alloc_frames(4) {
            heap_info!("  Allocated 4 contiguous frames starting at: {}", frames);
            dealloc_frames(frames, 4);
            heap_info!("  Deallocated 4 frames");
        }

        dealloc_frame(frame1);
        heap_info!("  Deallocated frame: {}", frame1);
    }

    print_frame_stats();
    heap_info!("=== Memory Management Demo Complete ===");
}


