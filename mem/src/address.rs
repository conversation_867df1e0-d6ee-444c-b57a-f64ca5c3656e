//! Address and page abstractions for memory management
//! 
//! This module provides types for physical and virtual addresses and pages.

use core::fmt;
use core::ops::{Add, Sub, AddAssign, SubAssign};

/// Page size in bytes (4KB)
pub const PAGE_SIZE: usize = 4096;

/// Page size shift (12 bits for 4KB pages)
pub const PAGE_SIZE_SHIFT: usize = 12;

/// Physical address type
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord)]
pub struct PhysAddr(pub usize);

/// Virtual address type
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord)]
pub struct VirtAddr(pub usize);

/// Physical page number
#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq, PartialOrd, Ord)]
pub struct PhysPageNum(pub usize);

/// Virtual page number
#[derive(Debug, <PERSON>lone, Co<PERSON>, PartialEq, Eq, PartialOrd, Ord)]
pub struct VirtPageNum(pub usize);

impl PhysAddr {
    /// Create a new physical address
    pub const fn new(addr: usize) -> Self {
        PhysAddr(addr)
    }

    /// Get the raw address value
    pub const fn as_usize(self) -> usize {
        self.0
    }

    /// Check if the address is page-aligned
    pub const fn is_aligned(self) -> bool {
        self.0 & (PAGE_SIZE - 1) == 0
    }

    /// Align down to page boundary
    pub const fn align_down(self) -> Self {
        PhysAddr(self.0 & !(PAGE_SIZE - 1))
    }

    /// Align up to page boundary
    pub const fn align_up(self) -> Self {
        PhysAddr((self.0 + PAGE_SIZE - 1) & !(PAGE_SIZE - 1))
    }

    /// Get the page number containing this address
    pub const fn page_number(self) -> PhysPageNum {
        PhysPageNum(self.0 >> PAGE_SIZE_SHIFT)
    }

    /// Get the offset within the page
    pub const fn page_offset(self) -> usize {
        self.0 & (PAGE_SIZE - 1)
    }

    /// Convert to a raw pointer
    pub const fn as_ptr<T>(self) -> *const T {
        self.0 as *const T
    }

    /// Convert to a mutable raw pointer
    pub const fn as_mut_ptr<T>(self) -> *mut T {
        self.0 as *mut T
    }
}

impl VirtAddr {
    /// Create a new virtual address
    pub const fn new(addr: usize) -> Self {
        VirtAddr(addr)
    }

    /// Get the raw address value
    pub const fn as_usize(self) -> usize {
        self.0
    }

    /// Check if the address is page-aligned
    pub const fn is_aligned(self) -> bool {
        self.0 & (PAGE_SIZE - 1) == 0
    }

    /// Align down to page boundary
    pub const fn align_down(self) -> Self {
        VirtAddr(self.0 & !(PAGE_SIZE - 1))
    }

    /// Align up to page boundary
    pub const fn align_up(self) -> Self {
        VirtAddr((self.0 + PAGE_SIZE - 1) & !(PAGE_SIZE - 1))
    }

    /// Get the page number containing this address
    pub const fn page_number(self) -> VirtPageNum {
        VirtPageNum(self.0 >> PAGE_SIZE_SHIFT)
    }

    /// Get the offset within the page
    pub const fn page_offset(self) -> usize {
        self.0 & (PAGE_SIZE - 1)
    }

    /// Convert to a raw pointer
    pub const fn as_ptr<T>(self) -> *const T {
        self.0 as *const T
    }

    /// Convert to a mutable raw pointer
    pub const fn as_mut_ptr<T>(self) -> *mut T {
        self.0 as *mut T
    }
}

impl PhysPageNum {
    /// Create a new physical page number
    pub const fn new(ppn: usize) -> Self {
        PhysPageNum(ppn)
    }

    /// Get the raw page number
    pub const fn as_usize(self) -> usize {
        self.0
    }

    /// Get the starting address of this page
    pub const fn start_address(self) -> PhysAddr {
        PhysAddr(self.0 << PAGE_SIZE_SHIFT)
    }

    /// Get the ending address of this page (exclusive)
    pub const fn end_address(self) -> PhysAddr {
        PhysAddr((self.0 + 1) << PAGE_SIZE_SHIFT)
    }

    /// Get the next page number
    pub const fn next(self) -> Self {
        PhysPageNum(self.0 + 1)
    }

    /// Get the previous page number
    pub const fn prev(self) -> Self {
        PhysPageNum(self.0 - 1)
    }
}

impl VirtPageNum {
    /// Create a new virtual page number
    pub const fn new(vpn: usize) -> Self {
        VirtPageNum(vpn)
    }

    /// Get the raw page number
    pub const fn as_usize(self) -> usize {
        self.0
    }

    /// Get the starting address of this page
    pub const fn start_address(self) -> VirtAddr {
        VirtAddr(self.0 << PAGE_SIZE_SHIFT)
    }

    /// Get the ending address of this page (exclusive)
    pub const fn end_address(self) -> VirtAddr {
        VirtAddr((self.0 + 1) << PAGE_SIZE_SHIFT)
    }

    /// Get the next page number
    pub const fn next(self) -> Self {
        VirtPageNum(self.0 + 1)
    }

    /// Get the previous page number
    pub const fn prev(self) -> Self {
        VirtPageNum(self.0 - 1)
    }
}

// Arithmetic operations for addresses
impl Add<usize> for PhysAddr {
    type Output = Self;
    fn add(self, rhs: usize) -> Self::Output {
        PhysAddr(self.0 + rhs)
    }
}

impl Sub<usize> for PhysAddr {
    type Output = Self;
    fn sub(self, rhs: usize) -> Self::Output {
        PhysAddr(self.0 - rhs)
    }
}

impl Sub<PhysAddr> for PhysAddr {
    type Output = usize;
    fn sub(self, rhs: PhysAddr) -> Self::Output {
        self.0 - rhs.0
    }
}

impl AddAssign<usize> for PhysAddr {
    fn add_assign(&mut self, rhs: usize) {
        self.0 += rhs;
    }
}

impl SubAssign<usize> for PhysAddr {
    fn sub_assign(&mut self, rhs: usize) {
        self.0 -= rhs;
    }
}

impl Add<usize> for VirtAddr {
    type Output = Self;
    fn add(self, rhs: usize) -> Self::Output {
        VirtAddr(self.0 + rhs)
    }
}

impl Sub<usize> for VirtAddr {
    type Output = Self;
    fn sub(self, rhs: usize) -> Self::Output {
        VirtAddr(self.0 - rhs)
    }
}

impl Sub<VirtAddr> for VirtAddr {
    type Output = usize;
    fn sub(self, rhs: VirtAddr) -> Self::Output {
        self.0 - rhs.0
    }
}

impl AddAssign<usize> for VirtAddr {
    fn add_assign(&mut self, rhs: usize) {
        self.0 += rhs;
    }
}

impl SubAssign<usize> for VirtAddr {
    fn sub_assign(&mut self, rhs: usize) {
        self.0 -= rhs;
    }
}

// Arithmetic operations for page numbers
impl Add<usize> for PhysPageNum {
    type Output = Self;
    fn add(self, rhs: usize) -> Self::Output {
        PhysPageNum(self.0 + rhs)
    }
}

impl Sub<usize> for PhysPageNum {
    type Output = Self;
    fn sub(self, rhs: usize) -> Self::Output {
        PhysPageNum(self.0 - rhs)
    }
}

impl Sub<PhysPageNum> for PhysPageNum {
    type Output = usize;
    fn sub(self, rhs: PhysPageNum) -> Self::Output {
        self.0 - rhs.0
    }
}

impl Add<usize> for VirtPageNum {
    type Output = Self;
    fn add(self, rhs: usize) -> Self::Output {
        VirtPageNum(self.0 + rhs)
    }
}

impl Sub<usize> for VirtPageNum {
    type Output = Self;
    fn sub(self, rhs: usize) -> Self::Output {
        VirtPageNum(self.0 - rhs)
    }
}

impl Sub<VirtPageNum> for VirtPageNum {
    type Output = usize;
    fn sub(self, rhs: VirtPageNum) -> Self::Output {
        self.0 - rhs.0
    }
}

// Display implementations
impl fmt::Display for PhysAddr {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        write!(f, "PhysAddr(0x{:x})", self.0)
    }
}

impl fmt::Display for VirtAddr {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        write!(f, "VirtAddr(0x{:x})", self.0)
    }
}

impl fmt::Display for PhysPageNum {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        write!(f, "PhysPageNum({})", self.0)
    }
}

impl fmt::Display for VirtPageNum {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        write!(f, "VirtPageNum({})", self.0)
    }
}



/// Utility functions for address calculations
pub mod utils {
    use super::*;

    /// Calculate the number of pages needed for a given size
    pub fn pages_needed(size: usize) -> usize {
        (size + PAGE_SIZE - 1) / PAGE_SIZE
    }

    /// Check if an address range is valid
    pub fn is_valid_range(start: PhysAddr, end: PhysAddr) -> bool {
        start <= end && start.is_aligned() && end.is_aligned()
    }

    /// Get page range for a memory region
    pub fn page_range(start: PhysAddr, size: usize) -> (PhysPageNum, PhysPageNum) {
        let start_page = start.align_down().page_number();
        let end_addr = start + size;
        let end_page = end_addr.align_up().page_number();
        (start_page, end_page)
    }
}
