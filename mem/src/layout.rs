//! Kernel memory layout and linker symbols
//!
//! This module provides access to linker symbols and kernel memory layout information.

use crate::address::{<PERSON>ysAddr, VirtAddr};
use config::VIRT_ADDR_OFFSET;

// External linker symbols
extern "C" {
    /// Start of kernel
    fn _skernel();
    /// End of kernel
    fn _end();
    /// Start of text section
    fn stext();
    /// End of text section
    fn etext();
    /// Start of rodata section
    fn srodata();
    /// End of rodata section
    fn erodata();
    /// Start of data section
    fn _sdata();
    /// End of data section
    fn _edata();
    /// Start of BSS section
    fn _sbss();
    /// End of BSS section
    fn _ebss();
}

/// Get kernel start address (virtual)
pub fn kernel_start_virt() -> VirtAddr {
    VirtAddr::new(_skernel as usize)
}

/// Get kernel end address (virtual)
pub fn kernel_end_virt() -> VirtAddr {
    VirtAddr::new(_end as usize)
}

/// Get kernel start address (physical)
pub fn kernel_start_phys() -> PhysAddr {
    let virt = kernel_start_virt();
    virt_to_phys(virt)
}

/// Get kernel end address (physical)
pub fn kernel_end_phys() -> PhysAddr {
    let virt = kernel_end_virt();
    virt_to_phys(virt)
}

/// Get text section range (virtual)
pub fn text_section_virt() -> (VirtAddr, VirtAddr) {
    (VirtAddr::new(stext as usize), VirtAddr::new(etext as usize))
}

/// Get rodata section range (virtual)
pub fn rodata_section_virt() -> (VirtAddr, VirtAddr) {
    (VirtAddr::new(srodata as usize), VirtAddr::new(erodata as usize))
}

/// Get data section range (virtual)
pub fn data_section_virt() -> (VirtAddr, VirtAddr) {
    (VirtAddr::new(_sdata as usize), VirtAddr::new(_edata as usize))
}

/// Get BSS section range (virtual)
pub fn bss_section_virt() -> (VirtAddr, VirtAddr) {
    (VirtAddr::new(_sbss as usize), VirtAddr::new(_ebss as usize))
}

/// Convert virtual address to physical address
pub fn virt_to_phys(vaddr: VirtAddr) -> PhysAddr {
    PhysAddr::new(vaddr.as_usize() - VIRT_ADDR_OFFSET)
}

/// Convert physical address to virtual address
pub fn phys_to_virt(paddr: PhysAddr) -> VirtAddr {
    VirtAddr::new(paddr.as_usize() + VIRT_ADDR_OFFSET)
}

/// Check if a physical address range overlaps with kernel
pub fn overlaps_with_kernel(start: PhysAddr, end: PhysAddr) -> bool {
    let kernel_start = kernel_start_phys();
    let kernel_end = kernel_end_phys();
    
    // Check for overlap: ranges overlap if start1 < end2 && start2 < end1
    start < kernel_end && kernel_start < end
}

/// Get kernel memory layout information
pub fn kernel_layout() -> KernelLayout {
    let text = text_section_virt();
    let rodata = rodata_section_virt();
    let data = data_section_virt();
    let bss = bss_section_virt();
    
    KernelLayout {
        kernel_start: kernel_start_virt(),
        kernel_end: kernel_end_virt(),
        text_start: text.0,
        text_end: text.1,
        rodata_start: rodata.0,
        rodata_end: rodata.1,
        data_start: data.0,
        data_end: data.1,
        bss_start: bss.0,
        bss_end: bss.1,
    }
}

/// Kernel memory layout information
#[derive(Debug, Clone)]
pub struct KernelLayout {
    pub kernel_start: VirtAddr,
    pub kernel_end: VirtAddr,
    pub text_start: VirtAddr,
    pub text_end: VirtAddr,
    pub rodata_start: VirtAddr,
    pub rodata_end: VirtAddr,
    pub data_start: VirtAddr,
    pub data_end: VirtAddr,
    pub bss_start: VirtAddr,
    pub bss_end: VirtAddr,
}

impl KernelLayout {
    /// Print kernel layout information
    pub fn print(&self) {
        use log::*;
        
        heap_info!("=== Kernel Memory Layout ===");
        heap_info!("Kernel: 0x{:x} - 0x{:x} ({} KB)", 
                  self.kernel_start.as_usize(), 
                  self.kernel_end.as_usize(),
                  (self.kernel_end.as_usize() - self.kernel_start.as_usize()) / 1024);
        heap_info!("  Text:   0x{:x} - 0x{:x}", self.text_start.as_usize(), self.text_end.as_usize());
        heap_info!("  RoData: 0x{:x} - 0x{:x}", self.rodata_start.as_usize(), self.rodata_end.as_usize());
        heap_info!("  Data:   0x{:x} - 0x{:x}", self.data_start.as_usize(), self.data_end.as_usize());
        heap_info!("  BSS:    0x{:x} - 0x{:x}", self.bss_start.as_usize(), self.bss_end.as_usize());
        
        // Also show physical addresses
        let phys_start = virt_to_phys(self.kernel_start);
        let phys_end = virt_to_phys(self.kernel_end);
        heap_info!("Physical: 0x{:x} - 0x{:x}", phys_start.as_usize(), phys_end.as_usize());
    }
}
