//! Heap allocator implementation for Echos OS
//! 
//! This module provides heap allocation using buddy_system_allocator.

use buddy_system_allocator::LockedHeap;
use log::*;
use config::HEAP_SIZE;

/// Global heap allocator
#[global_allocator]
static ALLOCATOR: LockedHeap<32> = LockedHeap::<32>::new();

/// Static heap memory area
static mut HEAP: [u8; HEAP_SIZE] = [0; HEAP_SIZE];

/// Initialize the heap allocator
pub fn init_heap() {
    unsafe {
        let heap_start = HEAP.as_ptr() as usize;
        let heap_end = heap_start + HEAP_SIZE;

        heap_info!("Initializing heap allocator");
        heap_info!("Heap start: 0x{:x}", heap_start);
        heap_info!("Heap end: 0x{:x}", heap_end);
        heap_info!("Heap size: {} KB", HEAP_SIZE / 1024);

        ALLOCATOR.lock().init(heap_start, HEAP_SIZE);

        heap_info!("Heap allocator initialized successfully");
    }
}

/// Get heap statistics
pub fn heap_stats() -> HeapStats {
    let allocator = ALLOCATOR.lock();
    let total_bytes = allocator.stats_total_bytes();
    let alloc_user = allocator.stats_alloc_user();
    
    HeapStats {
        total_size: HEAP_SIZE,
        used_size: alloc_user,
        free_size: total_bytes - alloc_user,
        total_bytes,
    }
}

/// Heap statistics
#[derive(Debug, Clone, Copy)]
pub struct HeapStats {
    /// Total heap size
    pub total_size: usize,
    /// Used heap size
    pub used_size: usize,
    /// Free heap size
    pub free_size: usize,
    /// Total managed bytes
    pub total_bytes: usize,
}

impl HeapStats {
    /// Get heap usage percentage
    pub fn usage_percent(&self) -> usize {
        if self.total_size == 0 {
            0
        } else {
            (self.used_size * 100) / self.total_size
        }
    }
}

/// Print heap statistics
pub fn print_heap_stats() {
    let stats = heap_stats();
    heap_info!("=== Heap Statistics ===");
    heap_info!("Total size: {} KB", stats.total_size / 1024);
    heap_info!("Used size: {} KB", stats.used_size / 1024);
    heap_info!("Free size: {} KB", stats.free_size / 1024);
    heap_info!("Usage: {}%", stats.usage_percent());
}

/// Get heap size from config
pub fn get_heap_size() -> usize {
    HEAP_SIZE
}

/// Check if heap is initialized
pub fn is_heap_initialized() -> bool {
    let stats = heap_stats();
    stats.total_bytes > 0
}
