//! Tests for address module

use crate::address::*;
use crate::layout::{phys_to_virt, virt_to_phys};
use log::*;

/// Test address creation and basic operations
pub fn test_address_creation() {
    heap_info!("Testing address creation and basic operations");
    
    // Test PhysAddr
    let paddr = PhysAddr::new(0x1000);
    assert_eq!(paddr.as_usize(), 0x1000);
    assert!(paddr.is_aligned());
    
    let unaligned = PhysAddr::new(0x1001);
    assert!(!unaligned.is_aligned());
    assert_eq!(unaligned.align_down(), PhysAddr::new(0x1000));
    assert_eq!(unaligned.align_up(), PhysAddr::new(0x2000));
    
    // Test VirtAddr
    let vaddr = VirtAddr::new(0x2000);
    assert_eq!(vaddr.as_usize(), 0x2000);
    assert!(vaddr.is_aligned());
    
    heap_info!("  ✓ Address creation and alignment");
}

/// Test page number operations
pub fn test_page_operations() {
    heap_info!("Testing page number operations");
    
    // Test PhysPageNum
    let ppn = PhysPageNum::new(1);
    assert_eq!(ppn.as_usize(), 1);
    assert_eq!(ppn.start_address(), PhysAddr::new(0x1000));
    assert_eq!(ppn.end_address(), PhysAddr::new(0x2000));
    assert_eq!(ppn.next(), PhysPageNum::new(2));
    assert_eq!(ppn.prev(), PhysPageNum::new(0));
    
    // Test VirtPageNum
    let vpn = VirtPageNum::new(2);
    assert_eq!(vpn.as_usize(), 2);
    assert_eq!(vpn.start_address(), VirtAddr::new(0x2000));
    assert_eq!(vpn.end_address(), VirtAddr::new(0x3000));
    
    heap_info!("  ✓ Page number operations");
}

/// Test address arithmetic
pub fn test_address_arithmetic() {
    heap_info!("Testing address arithmetic");
    
    let paddr = PhysAddr::new(0x1000);
    let paddr2 = paddr + 0x500;
    assert_eq!(paddr2.as_usize(), 0x1500);
    
    let diff = paddr2 - paddr;
    assert_eq!(diff, 0x500);
    
    let mut paddr3 = PhysAddr::new(0x2000);
    paddr3 += 0x1000;
    assert_eq!(paddr3.as_usize(), 0x3000);
    
    paddr3 -= 0x500;
    assert_eq!(paddr3.as_usize(), 0x2b00);
    
    heap_info!("  ✓ Address arithmetic");
}

/// Test page number arithmetic
pub fn test_page_arithmetic() {
    heap_info!("Testing page number arithmetic");
    
    let ppn = PhysPageNum::new(10);
    let ppn2 = ppn + 5;
    assert_eq!(ppn2.as_usize(), 15);
    
    let ppn3 = ppn2 - 3;
    assert_eq!(ppn3.as_usize(), 12);
    
    let diff = ppn2 - ppn;
    assert_eq!(diff, 5);
    
    heap_info!("  ✓ Page number arithmetic");
}

/// Test utility functions
pub fn test_utilities() {
    heap_info!("Testing utility functions");
    
    // Test pages_needed
    assert_eq!(utils::pages_needed(4096), 1);
    assert_eq!(utils::pages_needed(4097), 2);
    assert_eq!(utils::pages_needed(8192), 2);
    assert_eq!(utils::pages_needed(8193), 3);
    
    // Test is_valid_range
    let start = PhysAddr::new(0x1000);
    let end = PhysAddr::new(0x3000);
    assert!(utils::is_valid_range(start, end));
    
    let invalid_start = PhysAddr::new(0x1001);
    assert!(!utils::is_valid_range(invalid_start, end));
    
    // Test page_range
    let (start_page, end_page) = utils::page_range(PhysAddr::new(0x1500), 0x2000);
    assert_eq!(start_page, PhysPageNum::new(1)); // 0x1000 aligned down
    assert_eq!(end_page, PhysPageNum::new(4));   // 0x3500 aligned up to 0x4000
    
    heap_info!("  ✓ Utility functions");
}

/// Test address conversions
pub fn test_conversions() {
    heap_info!("Testing address conversions");
    
    let paddr = PhysAddr::new(0x12345678);
    let vaddr = phys_to_virt(paddr);
    assert_eq!(vaddr.as_usize(), 0x12345678);
    
    let paddr2 = virt_to_phys(vaddr);
    assert_eq!(paddr2.as_usize(), 0x12345678);
    
    heap_info!("  ✓ Address conversions");
}

/// Test page offset calculations
pub fn test_page_offset() {
    heap_info!("Testing page offset calculations");
    
    let addr = PhysAddr::new(0x1234);
    assert_eq!(addr.page_number(), PhysPageNum::new(1));
    assert_eq!(addr.page_offset(), 0x234);
    
    let addr2 = VirtAddr::new(0x5678);
    assert_eq!(addr2.page_number(), VirtPageNum::new(5));
    assert_eq!(addr2.page_offset(), 0x678);
    
    heap_info!("  ✓ Page offset calculations");
}

/// Run all address tests
pub fn test_address() {
    heap_info!("=== Address Module Tests ===");
    
    test_address_creation();
    test_page_operations();
    test_address_arithmetic();
    test_page_arithmetic();
    test_utilities();
    test_conversions();
    test_page_offset();
    
    heap_info!("=== Address Module Tests Complete ===");
}
