//! Comprehensive heap allocation tests
//! 
//! This module contains various tests to verify heap allocator functionality.

use crate::heap::*;
use log::*;

/// Run all heap tests
pub fn test_heap() {
    heap_info!("=== Starting Comprehensive Heap Tests ===");

    // Test 1: Basic allocation and deallocation
    test_basic_allocation();

    // Test 2: Large allocation test
    test_large_allocation();

    // Test 3: Fragmentation test
    test_fragmentation();

    // Test 4: Stress test
    test_stress_allocation();

    // Test 5: Memory leak detection
    test_memory_leak_detection();

    print_heap_stats();
    heap_info!("=== All Heap Tests Completed Successfully ===");
}

/// Test basic allocation and deallocation
fn test_basic_allocation() {
    heap_info!("Test 1: Basic allocation and deallocation");
    let initial_stats = heap_stats();

    {
        use alloc::vec::Vec;
        use alloc::string::String;

        // Test Vec allocation
        let mut vec = Vec::with_capacity(1000);
        for i in 0..1000 {
            vec.push(i);
        }
        assert_eq!(vec.len(), 1000);
        heap_info!("  ✓ Vec allocation (1000 elements)");

        // Test String allocation
        let mut s = String::with_capacity(1024);
        for _ in 0..100 {
            s.push_str("Hello, Echos OS! ");
        }
        assert!(s.len() > 1500);
        heap_info!("  ✓ String allocation ({} bytes)", s.len());

        // Test nested Vec
        let mut nested: Vec<Vec<u32>> = Vec::new();
        for i in 0..10 {
            let mut inner = Vec::new();
            for j in 0..100 {
                inner.push(i * 100 + j);
            }
            nested.push(inner);
        }
        assert_eq!(nested.len(), 10);
        heap_info!("  ✓ Nested Vec allocation (10x100 elements)");
    }
    
    // Check memory is freed after scope
    let final_stats = heap_stats();
    heap_info!("  ✓ Memory freed after scope (used: {} -> {} bytes)",
             initial_stats.used_size, final_stats.used_size);
}

/// Test large allocation
fn test_large_allocation() {
    heap_info!("Test 2: Large allocation test");
    let initial_stats = heap_stats();

    {
        use alloc::vec::Vec;

        // Try to allocate 1/4 of heap size
        let large_size = get_heap_size() / 4 / core::mem::size_of::<u64>();
        let mut large_vec: Vec<u64> = Vec::with_capacity(large_size);

        for i in 0..large_size {
            large_vec.push(i as u64);
        }

        assert_eq!(large_vec.len(), large_size);
        heap_info!("  ✓ Large allocation ({} KB)",
                 large_vec.len() * core::mem::size_of::<u64>() / 1024);

        // Verify data integrity
        for (i, &value) in large_vec.iter().enumerate() {
            assert_eq!(value, i as u64);
        }
        heap_info!("  ✓ Data integrity verified");
    }

    let final_stats = heap_stats();
    heap_info!("  ✓ Large allocation freed (used: {} -> {} bytes)",
             initial_stats.used_size, final_stats.used_size);
}

/// Test fragmentation handling
fn test_fragmentation() {
    heap_info!("Test 3: Fragmentation test");
    use alloc::vec::Vec;

    let mut allocations: Vec<Vec<u8>> = Vec::new();

    // Create many small allocations
    for i in 0..50 {
        let size = (i % 10 + 1) * 64; // 64 to 640 bytes
        let mut vec = Vec::with_capacity(size);
        for j in 0..size {
            vec.push((i + j) as u8);
        }
        allocations.push(vec);
    }

    heap_info!("  ✓ Created 50 small allocations");

    // Free every other allocation to create fragmentation
    let mut i = 0;
    allocations.retain(|_| {
        i += 1;
        i % 2 == 0
    });

    heap_info!("  ✓ Freed every other allocation (fragmentation created)");

    // Try to allocate in fragmented space
    for i in 0..25 {
        let size = 128;
        let mut vec = Vec::with_capacity(size);
        for j in 0..size {
            vec.push((i + j) as u8);
        }
        allocations.push(vec);
    }

    heap_info!("  ✓ Successfully allocated in fragmented space");
    print_heap_stats();
}

/// Stress test with many allocations
fn test_stress_allocation() {
    heap_info!("Test 4: Stress allocation test");
    use alloc::vec::Vec;
    use alloc::collections::BTreeMap;
    
    let mut stress_data: Vec<Vec<u32>> = Vec::new();
    let mut map: BTreeMap<u32, Vec<u8>> = BTreeMap::new();
    
    // Create many allocations of varying sizes
    for i in 0..100 {
        // Vec allocation
        let size = (i % 20 + 1) * 10;
        let mut vec = Vec::with_capacity(size);
        for j in 0..size {
            vec.push((i * 1000 + j) as u32);
        }
        stress_data.push(vec);
        
        // BTreeMap allocation
        let key = i as u32;
        let mut value = Vec::with_capacity(i % 50 + 10);
        for j in 0..(i % 50 + 10) {
            value.push((i + j) as u8);
        }
        map.insert(key, value);
    }
    
    heap_info!("  ✓ Created 100 Vec + 100 BTreeMap entries");
    
    // Verify data integrity
    for (i, vec) in stress_data.iter().enumerate() {
        let expected_size = (i % 20 + 1) * 10;
        assert_eq!(vec.len(), expected_size);
        for (j, &value) in vec.iter().enumerate() {
            assert_eq!(value, i as u32 * 1000 + j as u32);
        }
    }
    
    for (key, value) in &map {
        let expected_size = key % 50 + 10;
        assert_eq!(value.len(), expected_size as usize);
    }
    
    heap_info!("  ✓ Data integrity verified for all allocations");
    print_heap_stats();
}

/// Test for memory leak detection
fn test_memory_leak_detection() {
    heap_info!("Test 5: Memory leak detection");
    let initial_stats = heap_stats();

    // Perform simple allocations in a scope that should free everything
    {
        use alloc::vec::Vec;
        use alloc::string::String;

        // Simple Vec allocation
        let mut data = Vec::new();
        for i in 0..20 {
            data.push(i);
        }

        // Simple String allocation
        let mut s = String::new();
        s.push_str("Simple test string for leak detection");

        heap_info!("  ✓ Created temporary allocations");
    }

    // Check if memory was properly freed
    let final_stats = heap_stats();
    let leaked_bytes = final_stats.used_size.saturating_sub(initial_stats.used_size);

    if leaked_bytes == 0 {
        heap_info!("  ✓ No memory leaks detected");
    } else {
        heap_info!("  ⚠ Potential memory leak: {} bytes", leaked_bytes);
    }

    heap_info!("  Memory usage: {} -> {} bytes",
             initial_stats.used_size, final_stats.used_size);
}

/// Quick heap functionality test
pub fn quick_test() {
    heap_info!("Quick heap test...");

    use alloc::vec::Vec;
    let mut vec = Vec::new();
    for i in 0..10 {
        vec.push(i);
    }

    heap_info!("Quick test passed: {} elements", vec.len());
}
