//! Tests for frame allocator module

use crate::frame::*;
use crate::address::*;
use log::*;

/// Test memory segment creation
pub fn test_memory_segment() {
    heap_info!("Testing memory segment creation");
    
    let segment = MemorySegment::new(
        "test_segment",
        PhysAddr::new(0x100000),
        PhysAddr::new(0x200000),
        true
    );
    
    assert_eq!(segment.name, "test_segment");
    assert_eq!(segment.start, PhysAddr::new(0x100000));
    assert_eq!(segment.end, PhysAddr::new(0x200000));
    assert_eq!(segment.size(), 0x100000);
    assert_eq!(segment.page_count(), 0x100000 / PAGE_SIZE);
    assert!(segment.available);
    
    // Test contains
    assert!(segment.contains(PhysAddr::new(0x150000)));
    assert!(!segment.contains(PhysAddr::new(0x50000)));
    assert!(!segment.contains(PhysAddr::new(0x250000)));
    
    // Test page range
    let page_range = segment.page_range();
    assert_eq!(page_range.start, PhysPageNum::new(0x100000 / PAGE_SIZE));
    assert_eq!(page_range.end, PhysPageNum::new(0x200000 / PAGE_SIZE));
    
    heap_info!("  ✓ Memory segment creation and operations");
}

/// Test segmented frame allocator creation
pub fn test_allocator_creation() {
    heap_info!("Testing segmented frame allocator creation");
    
    let mut allocator = SegmentedFrameAllocator::new();
    
    // Initially empty
    assert_eq!(allocator.total_frames(), 0);
    assert_eq!(allocator.free_frames(), 0);
    assert_eq!(allocator.used_frames(), 0);
    
    // Add a segment
    let segment = MemorySegment::new(
        "main_memory",
        PhysAddr::new(0x100000),
        PhysAddr::new(0x200000),
        true
    );
    
    let expected_pages = segment.page_count();
    allocator.add_segment(segment);
    
    assert_eq!(allocator.total_frames(), expected_pages);
    assert_eq!(allocator.free_frames(), expected_pages);
    assert_eq!(allocator.used_frames(), 0);
    
    heap_info!("  ✓ Allocator creation and segment addition");
}

/// Test basic frame allocation
pub fn test_basic_allocation() {
    heap_info!("Testing basic frame allocation");
    
    let mut allocator = SegmentedFrameAllocator::new();
    
    // Add a small segment for testing
    let segment = MemorySegment::new(
        "test_memory",
        PhysAddr::new(0x100000),
        PhysAddr::new(0x110000), // 64KB = 16 pages
        true
    );
    
    allocator.add_segment(segment);
    
    let initial_free = allocator.free_frames();
    
    // Allocate a frame
    let frame1 = allocator.alloc().expect("Should allocate frame");
    assert_eq!(allocator.free_frames(), initial_free - 1);
    assert_eq!(allocator.used_frames(), 1);
    
    // Allocate another frame
    let frame2 = allocator.alloc().expect("Should allocate frame");
    assert_eq!(allocator.free_frames(), initial_free - 2);
    assert_eq!(allocator.used_frames(), 2);
    
    // Frames should be different
    assert_ne!(frame1, frame2);
    
    // Deallocate frames
    allocator.dealloc(frame1);
    assert_eq!(allocator.free_frames(), initial_free - 1);
    assert_eq!(allocator.used_frames(), 1);
    
    allocator.dealloc(frame2);
    assert_eq!(allocator.free_frames(), initial_free);
    assert_eq!(allocator.used_frames(), 0);
    
    heap_info!("  ✓ Basic frame allocation and deallocation");
}

/// Test contiguous frame allocation
pub fn test_contiguous_allocation() {
    heap_info!("Testing contiguous frame allocation");
    
    let mut allocator = SegmentedFrameAllocator::new();
    
    // Add a segment
    let segment = MemorySegment::new(
        "contiguous_test",
        PhysAddr::new(0x200000),
        PhysAddr::new(0x300000), // 1MB = 256 pages
        true
    );
    
    allocator.add_segment(segment);
    
    let initial_free = allocator.free_frames();
    
    // Allocate 4 contiguous frames
    let start_frame = allocator.alloc_contiguous(4).expect("Should allocate contiguous frames");
    assert_eq!(allocator.free_frames(), initial_free - 4);
    assert_eq!(allocator.used_frames(), 4);
    
    // Verify frames are contiguous
    for i in 1..4 {
        // We can't directly verify they were allocated, but we know they should be
        heap_debug!("Allocated frame: {}", start_frame + i);
    }
    
    // Deallocate contiguous frames
    allocator.dealloc_contiguous(start_frame, 4);
    assert_eq!(allocator.free_frames(), initial_free);
    assert_eq!(allocator.used_frames(), 0);
    
    heap_info!("  ✓ Contiguous frame allocation and deallocation");
}

/// Test multiple segments
pub fn test_multiple_segments() {
    heap_info!("Testing multiple memory segments");
    
    let mut allocator = SegmentedFrameAllocator::new();
    
    // Add multiple segments
    let segment1 = MemorySegment::new(
        "low_memory",
        PhysAddr::new(0x100000),
        PhysAddr::new(0x200000),
        true
    );
    
    let segment2 = MemorySegment::new(
        "high_memory",
        PhysAddr::new(0x400000),
        PhysAddr::new(0x500000),
        true
    );
    
    let segment3 = MemorySegment::new(
        "reserved_memory",
        PhysAddr::new(0x300000),
        PhysAddr::new(0x400000),
        false // Not available for allocation
    );
    
    let expected_pages1 = segment1.page_count();
    let expected_pages2 = segment2.page_count();
    let _expected_pages3 = segment3.page_count();
    
    allocator.add_segment(segment1);
    allocator.add_segment(segment2);
    allocator.add_segment(segment3);
    
    // Total frames includes all segments, but free frames only includes available ones
    assert_eq!(allocator.total_frames(), expected_pages1 + expected_pages2);
    assert_eq!(allocator.free_frames(), expected_pages1 + expected_pages2);
    
    // Allocate frames - should come from available segments
    let frame1 = allocator.alloc().expect("Should allocate frame");
    let frame2 = allocator.alloc().expect("Should allocate frame");
    
    // Find which segments the frames belong to
    let seg1_id = allocator.find_segment(frame1);
    let seg2_id = allocator.find_segment(frame2);
    
    assert!(seg1_id.is_some());
    assert!(seg2_id.is_some());
    
    // Frames should not be from the reserved segment (segment 2)
    assert_ne!(seg1_id, Some(2));
    assert_ne!(seg2_id, Some(2));
    
    heap_info!("  ✓ Multiple memory segments");
}

/// Test segment-specific allocation
pub fn test_segment_specific_allocation() {
    heap_info!("Testing segment-specific allocation");
    
    let mut allocator = SegmentedFrameAllocator::new();
    
    // Add two segments
    let segment1 = MemorySegment::new(
        "segment_0",
        PhysAddr::new(0x100000),
        PhysAddr::new(0x110000),
        true
    );
    
    let segment2 = MemorySegment::new(
        "segment_1",
        PhysAddr::new(0x200000),
        PhysAddr::new(0x210000),
        true
    );
    
    allocator.add_segment(segment1);
    allocator.add_segment(segment2);
    
    // Allocate from specific segment
    let frame_from_seg0 = allocator.alloc_from_segment(0).expect("Should allocate from segment 0");
    let frame_from_seg1 = allocator.alloc_from_segment(1).expect("Should allocate from segment 1");
    
    // Verify frames are from correct segments
    assert_eq!(allocator.find_segment(frame_from_seg0), Some(0));
    assert_eq!(allocator.find_segment(frame_from_seg1), Some(1));
    
    heap_info!("  ✓ Segment-specific allocation");
}

/// Test allocator exhaustion
pub fn test_allocator_exhaustion() {
    heap_info!("Testing allocator exhaustion");
    
    let mut allocator = SegmentedFrameAllocator::new();
    
    // Add a very small segment (just 2 pages)
    let segment = MemorySegment::new(
        "tiny_segment",
        PhysAddr::new(0x100000),
        PhysAddr::new(0x102000), // 8KB = 2 pages
        true
    );
    
    allocator.add_segment(segment);
    
    // Allocate all frames
    let frame1 = allocator.alloc().expect("Should allocate first frame");
    let _frame2 = allocator.alloc().expect("Should allocate second frame");

    // Should be exhausted now
    let frame3 = allocator.alloc();
    assert!(frame3.is_none(), "Should not allocate when exhausted");

    assert_eq!(allocator.free_frames(), 0);
    assert_eq!(allocator.used_frames(), 2);

    // Deallocate one frame
    allocator.dealloc(frame1);
    assert_eq!(allocator.free_frames(), 1);

    // Should be able to allocate again
    let _frame4 = allocator.alloc().expect("Should allocate after deallocation");
    assert_eq!(allocator.free_frames(), 0);
    
    heap_info!("  ✓ Allocator exhaustion handling");
}

/// Run all frame allocator tests
pub fn test_frame_allocator() {
    heap_info!("=== Frame Allocator Tests ===");
    
    test_memory_segment();
    test_allocator_creation();
    test_basic_allocation();
    test_contiguous_allocation();
    test_multiple_segments();
    test_segment_specific_allocation();
    test_allocator_exhaustion();
    
    heap_info!("=== Frame Allocator Tests Complete ===");
}
