# Memory Management Module

This module provides comprehensive memory management functionality for Echos OS, including heap allocation, address abstractions, and physical frame allocation.

## Directory Structure

```
mem/
├── Cargo.toml          # Package configuration
├── README.md           # This file
└── src/
    ├── lib.rs          # Module entry point and re-exports
    ├── heap.rs         # Core heap allocator implementation
    ├── address.rs      # Address and page abstractions
    ├── frame.rs        # Physical frame allocator
    └── tests/
        ├── mod.rs      # Test module entry point
        ├── heap.rs     # Comprehensive heap tests
        ├── address.rs  # Address operation tests
        └── frame.rs    # Frame allocator tests
```

## Components

### 1. Heap Allocator (`heap.rs`)
- Global heap allocator using `buddy_system_allocator`
- Heap initialization and statistics
- Architecture-specific heap sizes (4MB for both RISC-V and LoongArch64)
- Custom heap logging with purple color

### 2. Address Abstractions (`address.rs`)
- Physical and virtual address types (`PhysAddr`, `VirtAddr`)
- Page number types (`PhysPageNum`, `VirtPageNum`)
- Page size constants and utilities
- Address arithmetic and alignment operations

### 3. Frame Allocator (`frame.rs`)
- Segmented physical memory management
- Single and contiguous frame allocation
- Memory segment descriptors with availability flags
- Frame allocation statistics and monitoring

### 4. Comprehensive Tests
- Heap allocation tests (basic, large, fragmentation, stress, leak detection)
- Address operation tests (creation, arithmetic, utilities)
- Frame allocator tests (segments, allocation, exhaustion)

## Usage Examples

### Heap Management
```rust
use mem::{init_heap, heap_stats, print_heap_stats};

// Initialize the heap allocator
init_heap();

// Get heap statistics
let stats = heap_stats();
println!("Used: {} bytes, Free: {} bytes", stats.used_size, stats.free_size);

// Print detailed statistics
print_heap_stats();
```

### Address Operations
```rust
use mem::{PhysAddr, VirtAddr, PhysPageNum, PAGE_SIZE};

// Create addresses
let paddr = PhysAddr::new(0x100000);
let vaddr = VirtAddr::new(0x200000);

// Check alignment
if paddr.is_aligned() {
    println!("Address is page-aligned");
}

// Get page information
let page_num = paddr.page_number();
let offset = paddr.page_offset();

// Address arithmetic
let new_addr = paddr + 0x1000;
let diff = new_addr - paddr;
```

### Frame Allocation
```rust
use mem::{MemorySegment, PhysAddr, init_frame_allocator, alloc_frame, dealloc_frame};

// Define memory segments
let segments = vec![
    MemorySegment::new("main_memory", PhysAddr::new(0x100000), PhysAddr::new(0x200000), true),
    MemorySegment::new("reserved", PhysAddr::new(0x300000), PhysAddr::new(0x400000), false),
];

// Initialize frame allocator
init_frame_allocator(segments);

// Allocate frames
if let Some(frame) = alloc_frame() {
    println!("Allocated frame: {}", frame);
    dealloc_frame(frame);
}

// Allocate contiguous frames
if let Some(start_frame) = alloc_frames(4) {
    println!("Allocated 4 contiguous frames starting at: {}", start_frame);
    dealloc_frames(start_frame, 4);
}
```

## Testing

Run comprehensive tests for all components:

```rust
use mem::{test_heap, test_address, test_frame_allocator};

// Test heap functionality
test_heap();

// Test address operations
test_address();

// Test frame allocator
test_frame_allocator();
```

## Configuration

Heap sizes are configured per architecture in the `config` module:
- RISC-V: 4MB heap
- LoongArch64: 4MB heap

## Architecture Support

- **RISC-V 64-bit**: 4MB heap, standard page tables
- **LoongArch64**: 4MB heap, DMW memory mapping

## Constants

- `PAGE_SIZE` = 4096 bytes (4KB)
- `PAGE_SIZE_SHIFT` = 12 bits

## Features

- **Buddy System Allocator**: Efficient memory allocation with low fragmentation
- **Address Abstractions**: Type-safe physical and virtual address handling
- **Segmented Frame Allocation**: Support for multiple memory regions
- **Comprehensive Testing**: Multiple test scenarios to verify all components
- **Statistics**: Real-time heap and frame usage monitoring
- **Architecture-specific Configuration**: Different heap sizes per architecture
- **Memory Leak Detection**: Automated detection of potential memory leaks
- **Fragmentation Testing**: Verification of allocator behavior under fragmented conditions

## Logging

Custom logging with color coding:
- **HEAP logs**: Purple color (`[INFO HEAP]`)

## Integration

This module integrates with:
- **Boot module**: Memory initialization during system startup
- **Config module**: Architecture-specific memory configuration
- **Log module**: Custom colored logging for memory operations
