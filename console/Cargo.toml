[package]
name = "console"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true

[dependencies]
# Spin lock for synchronization
spin = { version = "0.9", default-features = false, features = ["mutex", "spin_mutex"] }
config = { path = "../config" }
lazy_static = { version = "1.4", features = ["spin_no_std"] }
# RISC-V SBI runtime - only for RISC-V targets
[target.'cfg(target_arch = "riscv64")'.dependencies]
sbi-rt = { workspace = true }

# NS16550A UART driver - only for LoongArch targets
[target.'cfg(target_arch = "loongarch64")'.dependencies]
ns16550a = { version = "0.3" }

[features]
default = []

[build-dependencies]
# For build script if needed
