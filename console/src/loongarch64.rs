//! LoongArch64 console implementation using NS16550A UART

use config::VIRT_ADDR_OFFSET;
use ns16550a::Uart;
use spin::Mutex;
use lazy_static::lazy_static;

/// Base address for NS16550A UART on LoongArch QEMU virt machine
const UART_BASE: usize = 0x01FE001E0 | VIRT_ADDR_OFFSET;

lazy_static! {
    /// Global UART instance (lazy initialized)
    static ref UART: Mutex<Uart> = Mutex::new(unsafe { Uart::new(UART_BASE) });
}

pub struct Console;

impl Console {
    /// Write a single character using NS16550A UART
    pub fn write_char(ch: u8) {
        let mut uart_guard = UART.lock();
        if ch == b'\n' {
            uart_guard.put(b'\r');
        }
        uart_guard.put(ch);
    }

    /// Write a string using NS16550A UART
    pub fn write_str(s: &str) {
        for byte in s.bytes() {
            Self::write_char(byte);
        }
    }

    /// Read a single character using NS16550A UART (blocking)
    pub fn read_char() -> u8 {
        loop {
            let mut uart_guard = UART.lock();

            if let Some(byte) = uart_guard.get() {
                return byte;
            }
            drop(uart_guard);
        }
    }
}
