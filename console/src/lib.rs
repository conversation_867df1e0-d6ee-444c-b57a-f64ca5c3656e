#![no_std]

// Architecture-specific console implementations
#[cfg(target_arch = "riscv64")]
pub mod riscv64;
#[cfg(target_arch = "riscv64")]
pub use riscv64::Console;

#[cfg(target_arch = "loongarch64")]
pub mod loongarch64;
#[cfg(target_arch = "loongarch64")]
pub use loongarch64::Console;

// Unified console interface
pub fn init() {
    // Console is now lazy-initialized, no explicit init needed
}

pub fn write_char(ch: u8) {
    Console::write_char(ch);
}

pub fn write_str(s: &str) {
    Console::write_str(s);
}

pub fn read_char() -> u8 {
    Console::read_char()
}

// Convenience macros for formatted output
#[macro_export]
macro_rules! print {
    ($($arg:tt)*) => {
        $crate::_print(format_args!($($arg)*))
    };
}

#[macro_export]
macro_rules! println {
    () => ($crate::print!("\n"));
    ($($arg:tt)*) => ($crate::print!("{}\n", format_args!($($arg)*)));
}

#[macro_export]
macro_rules! info {
    ($($arg:tt)*) => {
        $crate::println!("[INFO] {}", format_args!($($arg)*))
    };
}

#[macro_export]
macro_rules! debug {
    ($($arg:tt)*) => {
        $crate::println!("[DEBUG] {}", format_args!($($arg)*))
    };
}

#[macro_export]
macro_rules! warn {
    ($($arg:tt)*) => {
        $crate::println!("[WARN] {}", format_args!($($arg)*))
    };
}

#[macro_export]
macro_rules! error {
    ($($arg:tt)*) => {
        $crate::println!("[ERROR] {}", format_args!($($arg)*))
    };
}

// Internal print function
pub fn _print(args: core::fmt::Arguments) {
    use core::fmt::Write;
    struct Writer;

    impl Write for Writer {
        fn write_str(&mut self, s: &str) -> core::fmt::Result {
            Console::write_str(s);
            Ok(())
        }
    }

    Writer.write_fmt(args).unwrap();
}
