//! RISC-V console implementation using SBI

pub struct Console;

impl Console {
    /// Write a single character using SBI console
    pub fn write_char(ch: u8) {
        #[allow(deprecated)]
        sbi_rt::legacy::console_putchar(ch as usize);
    }

    /// Write a string using SBI console
    pub fn write_str(s: &str) {
        for byte in s.bytes() {
            Self::write_char(byte);
        }
    }

    /// Read a single character using SBI console (blocking)
    pub fn read_char() -> u8 {
        loop {
            #[allow(deprecated)]
            let ch = sbi_rt::legacy::console_getchar();
            if ch != usize::MAX {
                return ch as u8;
            }
            // No character available, continue polling
            core::hint::spin_loop();
        }
    }
}