# Console Module

Console模块为Echos OS提供统一的控制台输入输出和日志功能，支持多种架构。

## 架构支持

- **RISC-V 64-bit**: 使用SBI runtime进行控制台操作
- **LoongArch 64-bit**: 使用NS16550A UART驱动

## 特性

- 统一的控制台API，跨架构兼容
- 集成的日志系统
- 线程安全的输出
- 支持格式化输出

## 使用方法

### 基本输出

```rust
use console::{print, println};

// 打印文本
print!("Hello, ");
println!("World!");

// 格式化输出
println!("Number: {}, String: {}", 42, "test");
```

### 日志记录

#### 基本日志宏

```rust
use console::{info, debug, warn, error, trace};

info!("System initialized");
debug!("Debug information: {}", value);
warn!("Warning message");
error!("Error occurred: {}", error_msg);
trace!("Detailed trace information");
```

#### 分类日志宏

```rust
use console::{frame_log, device_log, memory_log, init_log};

// 内存帧相关日志
frame_log!(Info, "Allocated frame at 0x{:x}", addr);
frame_log!(Debug, "Frame ref count: {}", count);

// 设备驱动相关日志
device_log!(Info, "UART initialized");
device_log!(Warn, "Device timeout");

// 内存管理相关日志
memory_log!(Debug, "Heap size: {} bytes", size);
memory_log!(Error, "Out of memory");

// 初始化相关日志
init_log!(Info, "Kernel subsystem ready");
```

#### 可用的分类日志宏

- `frame_log!` - 内存帧管理
- `device_log!` - 设备驱动
- `memory_log!` - 内存管理
- `fs_log!` - 文件系统
- `process_log!` - 进程管理
- `interrupt_log!` - 中断处理
- `network_log!` - 网络栈
- `init_log!` - 初始化
- `syscall_log!` - 系统调用
- `scheduler_log!` - 调度器
- `boot_log!` - 启动过程

### 初始化

在kernel中初始化console：

```rust
// 在kernel启动时调用
console::init();
```

### 字符级操作

```rust
use console::{putchar, getchar, puts};

// 输出单个字符
putchar('A');

// 输出字符串
puts("Hello World");

// 读取字符（阻塞）
let ch = getchar();
```

## 架构特定实现

### RISC-V

使用SBI (Supervisor Binary Interface) legacy console functions:
- `console_putchar` - 字符输出
- `console_getchar` - 字符输入

### LoongArch

使用NS16550A UART直接寄存器访问:
- 基地址: `0x1fe001e0`
- 支持标准UART操作
- 自动处理换行符转换

## 日志开关配置

通过修改 `console/src/switch.rs` 中的常量来控制日志输出：

```rust
// 全局日志级别开关
pub const DEBUG_LOG: bool = true;    // 调试日志
pub const TRACE_LOG: bool = false;   // 跟踪日志（通常关闭）
pub const INFO_LOG: bool = true;     // 信息日志
pub const WARN_LOG: bool = true;     // 警告日志
pub const ERROR_LOG: bool = true;    // 错误日志

// 分类日志开关
pub const FRAME_LOG: bool = true;    // 内存帧日志
pub const DEVICE_LOG: bool = true;   // 设备日志
pub const MEMORY_LOG: bool = true;   // 内存管理日志
pub const FS_LOG: bool = true;       // 文件系统日志
// ... 更多分类
```

## 依赖

- `sbi-rt` (RISC-V only) - SBI runtime支持
- `ns16550a` (LoongArch only) - UART驱动
- `spin` - 自旋锁同步

## 编译

使用条件编译自动选择正确的架构实现：

```bash
# RISC-V构建
make ARCH=riscv64

# LoongArch构建
make ARCH=loongarch64
```

## 注意事项

- RISC-V使用的SBI legacy functions已被标记为deprecated，但仍然可用
- LoongArch实现使用lazy initialization来避免静态初始化问题
- 所有输出操作都是线程安全的
- 输入操作是阻塞的
- 日志开关在编译时确定，没有运行时开销
- 可以通过修改 `switch.rs` 来精确控制哪些日志输出

## 性能特性

- **零运行时开销**: 禁用的日志在编译时完全移除
- **分类控制**: 可以只启用特定子系统的日志
- **级别控制**: 可以按日志级别过滤输出
- **线程安全**: 使用自旋锁保护并发访问
