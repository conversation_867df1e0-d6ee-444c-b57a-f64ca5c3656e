#![no_std]

//! Configuration module for Echos OS
//!
//! This module provides architecture-specific constants and configurations
//! with a unified interface. Users can access constants directly through
//! the config module without needing to know the specific architecture.

// Architecture-specific modules (kept for compatibility)
#[cfg(target_arch = "riscv64")]
pub mod riscv64;

#[cfg(target_arch = "loongarch64")]
pub mod loongarch64;

// Unified exports - automatically select the correct architecture
#[cfg(target_arch = "riscv64")]
pub use riscv64::*;

#[cfg(target_arch = "loongarch64")]
pub use loongarch64::*;
