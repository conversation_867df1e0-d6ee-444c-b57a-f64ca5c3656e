# 模块导出统一访问指南

本文档说明了Echos OS中各个模块的统一导出方式，使得代码可以跨架构使用而无需关心具体的架构细节。

## Config模块

### 统一访问方式
```rust
use config::{STACK_SIZE, VIRT_ADDR_OFFSET};

// 直接使用，无需指定架构
let stack_size = STACK_SIZE;
let virt_offset = VIRT_ADDR_OFFSET;
```

### 架构特定访问方式（兼容性保留）
```rust
// RISC-V
use config::riscv64::{STACK_SIZE, VIRT_ADDR_OFFSET};

// LoongArch64
use config::loongarch64::{STACK_SIZE, VIRT_ADDR_OFFSET};
```

### 架构特定值
- **RISC-V 64**: 
  - `VIRT_ADDR_OFFSET = 0xffff_ffc0_0000_0000`
  - `STACK_SIZE = 16 * 1024`
- **LoongArch 64**: 
  - `VIRT_ADDR_OFFSET = 0x9000_0000_0000_0000`
  - `STACK_SIZE = 16 * 1024`

## Arch模块

### 统一访问方式
```rust
use arch::power::shutdown;

// 直接调用，自动选择正确的架构实现
shutdown();
```

### 架构特定访问方式
```rust
// 通过arch别名访问
use arch::arch::power::shutdown;

// 或直接访问特定架构
use arch::riscv64::power::shutdown;    // RISC-V
use arch::loongarch64::power::shutdown; // LoongArch64
```

### 实现细节
- **RISC-V 64**: 使用SBI legacy shutdown
- **LoongArch 64**: 使用ACPI GED寄存器 + idle指令

## Console模块

### 统一访问方式
```rust
use console::{println, info, debug, warn, error};

// 跨架构统一接口
println!("Hello, World!");
info!("System information");
```

### 架构抽象
Console模块内部使用`arch_console`别名来抽象不同架构的实现：
- **RISC-V 64**: 使用SBI runtime
- **LoongArch 64**: 使用NS16550A UART

## 使用建议

1. **优先使用统一接口**: 除非有特殊需求，否则应该使用统一的导出接口
2. **架构无关代码**: 使用统一接口编写的代码可以在所有支持的架构上运行
3. **架构特定优化**: 只有在需要架构特定优化时才直接访问架构模块

## 示例：跨架构代码

```rust
use config::{STACK_SIZE, VIRT_ADDR_OFFSET};
use console::{println, info};
use arch::power::shutdown;

fn kernel_main() {
    // 使用统一的配置常量
    println!("Stack size: {}", STACK_SIZE);
    println!("Virtual address offset: 0x{:x}", VIRT_ADDR_OFFSET);
    
    // 使用统一的日志接口
    info!("Kernel initialized successfully");
    
    // 使用统一的关机接口
    shutdown();
}
```

这种设计使得同一份代码可以在RISC-V和LoongArch64架构上运行，而无需修改。
