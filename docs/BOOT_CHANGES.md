# Boot 启动流程更改

本文档记录了对boot启动流程的重要更改，特别是移除页表和地址映射操作。

## 主要更改

### 1. 移除页表和虚拟内存管理

**RISC-V (boot/src/riscv64/entry64.asm)**:
- 移除了 `setup_page_tables` 函数
- 移除了 `page_table_root` 数据结构
- 移除了虚拟内存启用代码 (satp寄存器设置)
- 移除了 `sfence.vma` 指令

**LoongArch (boot/src/loongarch64/entry64.asm)**:
- 移除了 `setup_page_tables` 函数  
- 移除了 `page_table_root` 数据结构
- 移除了虚拟内存启用代码 (PGDL寄存器设置)

### 2. 修复Hart ID获取问题

**问题**: 原始代码使用 `csrr a0, mhartid` 在supervisor模式下访问machine模式寄存器，导致非法指令异常。

**解决方案**: 
- 使用SBI传递的参数：`a0` = hart ID, `a1` = DTB地址
- 在汇编代码中保存这些参数到 `s0` 和 `s1` 寄存器
- 传递给 `kernel_init` 函数作为参数

### 3. 更新函数签名

**kernel_init函数**:
```rust
// 之前
pub extern "C" fn kernel_init()

// 现在  
pub extern "C" fn kernel_init(hart_id: usize, dtb_addr: usize)
```

### 4. 修复BSS段问题

**问题**: BSS段清除代码试图清除错误的内存区域，导致存储故障。

**解决方案**: 
- 更新linker script，添加适当的对齐
- 确保BSS段符号正确定义

## 当前启动流程

### RISC-V启动流程

1. **SBI启动**: OpenSBI加载内核并跳转到 `_start`
2. **参数保存**: 保存hart ID (a0) 和DTB地址 (a1)
3. **基本设置**: 
   - 禁用中断
   - 设置supervisor模式
   - 检查hart ID (只有hart 0继续)
4. **内存初始化**:
   - 设置栈指针
   - 清除BSS段
5. **内核初始化**: 调用 `kernel_init(hart_id, dtb_addr)`
6. **主内核**: 调用 `rust_main()`

### LoongArch启动流程

1. **Bootloader启动**: 加载内核并跳转到 `_start`
2. **参数保存**: 保存CPU ID (a0) 和DTB地址 (a1)
3. **基本设置**:
   - 禁用中断
   - 检查CPU ID (只有CPU 0继续)
4. **内存初始化**:
   - 设置栈指针
   - 清除BSS段
5. **模式设置**: 设置CRMD寄存器为内核模式
6. **内核初始化**: 调用 `kernel_init(cpu_id, dtb_addr)`
7. **主内核**: 调用 `rust_main()`

## 内存布局

当前内存布局使用**物理地址**，没有虚拟内存映射：

### RISC-V内存布局
- 内核加载地址: `0x80200000`
- 栈空间: 16KB
- BSS段: 动态分配

### LoongArch内存布局  
- 内核加载地址: `0x90000000`
- 栈空间: 16KB
- BSS段: 动态分配

## 优势

1. **简化启动**: 移除复杂的页表设置，启动更快更可靠
2. **调试友好**: 物理地址直接对应，便于调试
3. **减少错误**: 避免虚拟内存相关的复杂错误
4. **灵活性**: 后续可以在Rust代码中实现更复杂的内存管理

## 注意事项

1. **物理内存访问**: 当前所有内存访问都是物理地址
2. **内存保护**: 没有虚拟内存保护，需要小心内存访问
3. **地址空间**: 内核和用户空间共享同一地址空间
4. **后续扩展**: 虚拟内存管理可以在内核初始化后实现

## 测试结果

- ✅ RISC-V: 成功启动，console输出正常
- ✅ LoongArch: 成功构建
- ✅ 异常修复: 消除了非法指令和存储故障异常
- ✅ 日志系统: 正常显示hart ID和DTB地址信息
