# QEMU 日志和异常调试指南

本文档介绍如何使用Echos OS的QEMU日志功能来调试内核异常和错误。

## 日志模式

### 1. 标准日志模式 (`make runlog`)

```bash
make runlog
```

**包含的日志类型:**
- `in_asm` - 执行的汇编指令
- `cpu_reset` - CPU重置事件
- `guest_errors` - 客户机错误
- `int` - 中断和异常
- `exec` - 指令执行
- `cpu` - CPU状态变化
- `mmu` - 内存管理单元操作
- `pcall` - 过程调用
- `unimp` - 未实现的功能

### 2. 异常专注模式 (`make runlog-exception`)

```bash
make runlog-exception
```

**专注于异常调试:**
- `int` - 中断和异常处理
- `cpu_reset` - CPU重置
- `guest_errors` - 客户机错误
- `exec` - 指令执行上下文
- `cpu` - CPU状态变化
- `mmu` - MMU故障和操作
- `unimp` - 未实现功能调用
- `trace:*` - 所有跟踪事件

### 3. 详细日志模式 (`make runlog-verbose`)

```bash
make runlog-verbose
```

**包含所有可能的日志:**
- `in_asm` / `out_asm` - 输入/输出汇编指令
- `int` - 中断和异常
- `exec` - 指令执行
- `cpu` - CPU状态
- `mmu` - 内存管理
- `pcall` - 过程调用
- `cpu_reset` - CPU重置
- `guest_errors` - 客户机错误
- `unimp` - 未实现功能
- `trace:*` - 所有跟踪事件

## 日志分析

### 自动分析工具

```bash
make analyze-log
```

这个命令会自动分析`qemu.log`文件并提取：

1. **异常和中断**: 搜索exception、interrupt、fault、trap关键词
2. **客户机错误**: 搜索guest_error、invalid、illegal关键词  
3. **MMU故障**: 搜索mmu、tlb、page fault关键词
4. **CPU状态变化**: 显示CPU状态和模式变化
5. **最后20行日志**: 显示最近的日志条目

### 手动分析

#### 查找异常
```bash
grep -i "exception\|interrupt\|fault\|trap" qemu.log
```

#### 查找内存错误
```bash
grep -i "mmu\|tlb\|page.*fault" qemu.log
```

#### 查找非法指令
```bash
grep -i "illegal\|invalid\|unimp" qemu.log
```

#### 查看CPU状态变化
```bash
grep -i "cpu.*state\|mode.*change" qemu.log
```

## 常见异常类型

### RISC-V异常

1. **指令地址不对齐** - 跳转到非对齐地址
2. **指令访问故障** - 访问无效指令地址
3. **非法指令** - 执行未定义指令
4. **断点** - 遇到断点指令
5. **加载地址不对齐** - 加载非对齐数据
6. **加载访问故障** - 加载无效地址数据
7. **存储地址不对齐** - 存储到非对齐地址
8. **存储访问故障** - 存储到无效地址
9. **环境调用** - ecall指令
10. **指令页故障** - 指令页面错误
11. **加载页故障** - 加载页面错误
12. **存储页故障** - 存储页面错误

### LoongArch异常

1. **中断** - 外部中断
2. **PIL** - 页无效异常
3. **PIS** - 页无效异常(存储)
4. **PIF** - 页无效异常(取指)
5. **PME** - 页修改异常
6. **PNR** - 页不可读异常
7. **PNX** - 页不可执行异常
8. **PPI** - 页特权等级异常
9. **ADEF** - 地址错误异常(取指)
10. **ADEM** - 地址错误异常(内存访问)

## 调试工作流

1. **运行带日志的内核**:
   ```bash
   make runlog-exception
   ```

2. **分析日志**:
   ```bash
   make analyze-log
   ```

3. **如果需要更详细信息**:
   ```bash
   make runlog-verbose
   make analyze-log
   ```

4. **使用GDB调试**:
   ```bash
   make debug
   # 在另一个终端
   gdb-multiarch -ex 'target remote :1234' target/riscv64gc-unknown-none-elf/debug/kernel
   ```

## 日志文件管理

- 日志文件: `qemu.log`
- 清理日志: `make clean` (会删除qemu.log)
- 日志会在每次运行时覆盖

## 性能注意事项

- `runlog-verbose`会产生大量日志，可能影响性能
- 对于长时间运行，建议使用`runlog-exception`
- 日志文件可能会变得很大，定期清理
