[package]
name = "boot"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true

[dependencies]
console = { path = "../console" }
config = { path = "../config" }
bitflags = { workspace = true }
arch = { path = "../arch" }
log = { path = "../log" }
mem = { path = "../mem" }
dtb = { path = "../dtb" }

[target.'cfg(target_arch = "riscv64")'.dependencies]
riscv = { workspace = true }

[features]
default = []
riscv64 = []
loongarch64 = []

[build-dependencies]
# For build script if needed
