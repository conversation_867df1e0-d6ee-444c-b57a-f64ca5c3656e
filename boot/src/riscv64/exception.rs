//! RISC-V 64-bit Exception Handling

use core::arch::naked_asm;
use log::*;

/// RISC-V Exception Codes
#[derive(Debug, <PERSON><PERSON>, Co<PERSON>)]
#[repr(usize)]
pub enum ExceptionCode {
    /// Instruction address misaligned
    InstructionAddressMisaligned = 0,
    /// Instruction access fault
    InstructionAccessFault = 1,
    /// Illegal instruction
    IllegalInstruction = 2,
    /// Breakpoint
    Breakpoint = 3,
    /// Load address misaligned
    LoadAddressMisaligned = 4,
    /// Load access fault
    LoadAccessFault = 5,
    /// Store/AMO address misaligned
    StoreAddressMisaligned = 6,
    /// Store/AMO access fault
    StoreAccessFault = 7,
    /// Environment call from U-mode
    EnvironmentCallFromUMode = 8,
    /// Environment call from S-mode
    EnvironmentCallFromSMode = 9,
    /// Environment call from M-mode
    EnvironmentCallFromMMode = 11,
    /// Instruction page fault
    InstructionPageFault = 12,
    /// Load page fault
    LoadPageFault = 13,
    /// Store/AMO page fault
    StorePageFault = 15,
    /// Unknown exception
    Unknown = 0xFFFF,
}

impl ExceptionCode {
    /// Convert exception code number to enum
    pub fn from_code(code: usize) -> Self {
        match code {
            0 => ExceptionCode::InstructionAddressMisaligned,
            1 => ExceptionCode::InstructionAccessFault,
            2 => ExceptionCode::IllegalInstruction,
            3 => ExceptionCode::Breakpoint,
            4 => ExceptionCode::LoadAddressMisaligned,
            5 => ExceptionCode::LoadAccessFault,
            6 => ExceptionCode::StoreAddressMisaligned,
            7 => ExceptionCode::StoreAccessFault,
            8 => ExceptionCode::EnvironmentCallFromUMode,
            9 => ExceptionCode::EnvironmentCallFromSMode,
            11 => ExceptionCode::EnvironmentCallFromMMode,
            12 => ExceptionCode::InstructionPageFault,
            13 => ExceptionCode::LoadPageFault,
            15 => ExceptionCode::StorePageFault,
            _ => ExceptionCode::Unknown,
        }
    }

    /// Get human-readable description
    pub fn description(&self) -> &'static str {
        match self {
            ExceptionCode::InstructionAddressMisaligned => "Instruction address misaligned",
            ExceptionCode::InstructionAccessFault => "Instruction access fault",
            ExceptionCode::IllegalInstruction => "Illegal instruction",
            ExceptionCode::Breakpoint => "Breakpoint",
            ExceptionCode::LoadAddressMisaligned => "Load address misaligned",
            ExceptionCode::LoadAccessFault => "Load access fault",
            ExceptionCode::StoreAddressMisaligned => "Store/AMO address misaligned",
            ExceptionCode::StoreAccessFault => "Store/AMO access fault",
            ExceptionCode::EnvironmentCallFromUMode => "Environment call from U-mode",
            ExceptionCode::EnvironmentCallFromSMode => "Environment call from S-mode",
            ExceptionCode::EnvironmentCallFromMMode => "Environment call from M-mode",
            ExceptionCode::InstructionPageFault => "Instruction page fault",
            ExceptionCode::LoadPageFault => "Load page fault",
            ExceptionCode::StorePageFault => "Store/AMO page fault",
            ExceptionCode::Unknown => "Unknown exception",
        }
    }
}

/// Exception context structure
#[derive(Debug)]
#[repr(C)]
pub struct ExceptionContext {
    /// General purpose registers x0-x31
    pub regs: [usize; 32],
    /// Supervisor exception program counter
    pub sepc: usize,
    /// Supervisor status register
    pub sstatus: usize,
    /// Supervisor cause register
    pub scause: usize,
    /// Supervisor trap value register
    pub stval: usize,
}

impl ExceptionContext {
    /// Get exception code from scause register
    pub fn exception_code(&self) -> ExceptionCode {
        let code = self.scause & 0x7FFFFFFFFFFFFFFF; // Clear interrupt bit
        ExceptionCode::from_code(code)
    }

    /// Check if exception is an interrupt
    pub fn is_interrupt(&self) -> bool {
        (self.scause & (1 << 63)) != 0
    }


}

/// Main exception handler
#[no_mangle]
pub extern "C" fn riscv_exception_handler(ctx: &mut ExceptionContext) {
    if ctx.is_interrupt() {
        boot_info!("Interrupt: 0x{:x}", ctx.scause);
        return;
    }

    let exc_code = ctx.exception_code();
    boot_error!("Exception: {} at 0x{:x}", exc_code.description(), ctx.sepc);

    match exc_code {
        ExceptionCode::IllegalInstruction => {
            boot_error!("Illegal instruction: 0x{:08x}", ctx.stval as u32);
        }
        ExceptionCode::InstructionPageFault | ExceptionCode::LoadPageFault | ExceptionCode::StorePageFault => {
            boot_error!("Page fault: 0x{:x}", ctx.stval);
        }
        _ => {}
    }

    boot_error!("System halting");
    arch::power::shutdown();
}

/// Exception vector table entry
#[unsafe(naked)]
#[no_mangle]
unsafe extern "C" fn riscv_exception_entry() {
    naked_asm!(
        // Save all registers to stack
        "addi sp, sp, -280",  // sizeof(ExceptionContext)
        
        // Save general purpose registers
        "sd x0,  0(sp)",
        "sd x1,  8(sp)", 
        "sd x2,  16(sp)",
        "sd x3,  24(sp)",
        "sd x4,  32(sp)",
        "sd x5,  40(sp)",
        "sd x6,  48(sp)",
        "sd x7,  56(sp)",
        "sd x8,  64(sp)",
        "sd x9,  72(sp)",
        "sd x10, 80(sp)",
        "sd x11, 88(sp)",
        "sd x12, 96(sp)",
        "sd x13, 104(sp)",
        "sd x14, 112(sp)",
        "sd x15, 120(sp)",
        "sd x16, 128(sp)",
        "sd x17, 136(sp)",
        "sd x18, 144(sp)",
        "sd x19, 152(sp)",
        "sd x20, 160(sp)",
        "sd x21, 168(sp)",
        "sd x22, 176(sp)",
        "sd x23, 184(sp)",
        "sd x24, 192(sp)",
        "sd x25, 200(sp)",
        "sd x26, 208(sp)",
        "sd x27, 216(sp)",
        "sd x28, 224(sp)",
        "sd x29, 232(sp)",
        "sd x30, 240(sp)",
        "sd x31, 248(sp)",
        
        // Save exception-related CSRs
        "csrr t0, sepc",
        "sd t0, 256(sp)",
        "csrr t0, sstatus",
        "sd t0, 264(sp)",
        "csrr t0, scause",
        "sd t0, 272(sp)",
        "csrr t0, stval",
        "sd t0, 280(sp)",
        
        // Call exception handler with context pointer
        "mv a0, sp",
        "call {handler}",
        
        // Should not return, but just in case
        "1: j 1b",
        
        handler = sym riscv_exception_handler,
    );
}

/// Initialize exception handling
pub fn init_exception_handling() {
    unsafe {
        core::arch::asm!(
            "la t0, {entry}",
            "csrw stvec, t0",
            entry = sym riscv_exception_entry,
            out("t0") _,
        );
    }
    boot_info!("Exceptions ready");
}
