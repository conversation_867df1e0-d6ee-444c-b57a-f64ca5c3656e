use core::arch::naked_asm;
use config::STACK_SIZE;
use log::*;
use mem;
use dtb::parser::DtbParser;

mod exception;
use exception::init_loongarch_system;


macro_rules! init_dwm {
    () => {
        "
        ori         $t0, $zero, 0x1     # CSR_DMW1_PLV0
        lu52i.d     $t0, $t0, -2048     # UC, PLV0, 0x8000 xxxx xxxx xxxx
        csrwr       $t0, 0x180          # LOONGARCH_CSR_DMWIN0
        ori         $t0, $zero, 0x11    # CSR_DMW1_MAT | CSR_DMW1_PLV0
        lu52i.d     $t0, $t0, -1792     # CA, PLV0, 0x9000 xxxx xxxx xxxx
        csrwr       $t0, 0x181          # LOONGARCH_CSR_DMWIN1
        "
    };
}


/// The earliest entry point for the primary CPU.
///
/// We can't use bl to jump to higher address, so we use jirl to jump to higher address.
///
/// LoongArch64 boot protocol:
/// - a0: CPU ID (from bootloader)
/// - a1: DTB address (from bootloader)
#[unsafe(naked)]
#[no_mangle]
#[link_section = ".text.entry"]
unsafe extern "C" fn _start() -> ! {
    naked_asm!(
        init_dwm!(),
        "# Enable PG
        li.w        $t0, 0xb0       # PLV=0, IE=0, PG=1
        csrwr       $t0, 0x0        # LOONGARCH_CSR_CRMD
        li.w        $t0, 0x00       # PLV=0, PIE=0, PWE=0
        csrwr       $t0, 0x1        # LOONGARCH_CSR_PRMD
        li.w        $t0, 0x00       # FPE=0, SXE=0, ASXE=0, BTE=0
        csrwr       $t0, 0x2        # LOONGARCH_CSR_EUEN

        la.global   $sp, bstack_top
        # a0 and a1 are already set by bootloader
        # a0: CPU ID, a1: DTB address
        la.global   $t0, {entry}
        jirl        $zero,$t0,0
        ",
        entry = sym arch_init,
    )
}

#[unsafe(link_section = ".bss.bstack ")]
static mut B_STACK: [u8; STACK_SIZE] = [0; STACK_SIZE];



pub fn arch_init(hart_id: usize, _dtb_addr: usize) {
    boot_info!("LoongArch64 init (hart: {})", hart_id);

    // Initialize heap allocator
    mem::init_heap();

    // Initialize LoongArch system (TLB, exceptions, DMW) first
    init_loongarch_system();

    // Parse DTB at fixed address with DMW mapping
    // DMW1 maps 0x9000_0000_0000_0000 + physical_addr
    let dtb_addr = 0x9000000000100000;  // DMW1 + 0x100000
    boot_info!("Parsing DTB at address: 0x{:x}", dtb_addr);
    try_parse_dtb(dtb_addr);

    boot_info!("Init complete, shutting down");
    arch::power::shutdown();
}

/// Try to parse DTB at given address
fn try_parse_dtb(dtb_addr: usize) {
    parse_and_display_dtb(dtb_addr);
}

/// Parse and display DTB information
fn parse_and_display_dtb(dtb_addr: usize) {
    if dtb_addr == 0 {
        boot_info!("No DTB provided (dtb_addr = 0)");
        return;
    }

    boot_info!("Checking DTB at address: 0x{:x}", dtb_addr);

    // Check if the address is accessible by reading the magic number first
    let magic = unsafe {
        let magic_ptr = dtb_addr as *const u8;
        let magic_bytes = core::slice::from_raw_parts(magic_ptr, 4);
        u32::from_be_bytes([magic_bytes[0], magic_bytes[1], magic_bytes[2], magic_bytes[3]])
    };

    boot_info!("DTB magic: 0x{:08x}", magic);

    if magic != 0xd00dfeed {
        boot_info!("Invalid DTB magic, expected 0xd00dfeed, got 0x{:08x}", magic);
        return;
    }

    // Try to determine DTB size by reading the header
    let dtb_size = unsafe {
        // Read the total size from DTB header (offset 4, big-endian u32)
        let size_ptr = (dtb_addr + 4) as *const u8;
        let size_bytes = core::slice::from_raw_parts(size_ptr, 4);
        u32::from_be_bytes([size_bytes[0], size_bytes[1], size_bytes[2], size_bytes[3]]) as usize
    };

    boot_info!("DTB address: 0x{:x}, size: {} bytes", dtb_addr, dtb_size);

    // Create DTB parser
    match unsafe { DtbParser::from_memory(dtb_addr, dtb_size) } {
        Ok(mut parser) => {
            // Parse the DTB
            match parser.parse() {
                Ok(()) => {
                    let dtb_info = parser.get_info();

                    // Display DTB information using the built-in method
                    dtb_info.display();

                    // Initialize frame allocator from DTB memory information
                    // Use simplified version for LoongArch64 to avoid vector instruction issues
                    crate::memory::init_frame_allocator_from_dtb_simple(&dtb_info.memory_regions);
                }
                Err(e) => {
                    boot_error!("DTB parsing failed: {:?}", e);
                }
            }
        }
        Err(e) => {
            boot_error!("Failed to create DTB parser: {:?}", e);
        }
    }
}