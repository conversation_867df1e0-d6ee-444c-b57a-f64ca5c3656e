//! LoongArch64 Exception Handling

use core::arch::naked_asm;
use log::*;

// LoongArch64 constants
const PS_4K: u32 = 12;  // 4KB page size (2^12)
const PAGE_SIZE_SHIFT: u32 = 12;  // 4KB page size shift



/// LoongArch64 Exception Codes
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>)]
#[repr(u32)]
pub enum ExceptionCode {
    /// Interrupt
    Interrupt = 0,
    /// Load operation page invalid exception
    LoadPageInvalid = 1,
    /// Store operation page invalid exception  
    StorePageInvalid = 2,
    /// Instruction fetch page invalid exception
    FetchPageInvalid = 3,
    /// Page modification exception
    PageModification = 4,
    /// Page non-readable exception
    PageNonReadable = 5,
    /// Page non-writable exception
    PageNonWritable = 6,
    /// Page non-executable exception
    PageNonExecutable = 7,
    /// Address error exception for instruction fetch
    AddressErrorFetch = 8,
    /// Address error exception for memory access
    AddressErrorMemory = 9,
    /// System call exception
    SystemCall = 11,
    /// Breakpoint exception
    Breakpoint = 12,
    /// Instruction non-defined exception
    InstructionUndefined = 13,
    /// Instruction privilege error exception
    InstructionPrivilege = 14,
    /// Floating point disabled exception
    FloatingPointDisabled = 15,
    /// 128-bit vector instructions disabled exception
    VectorInstructionsDisabled = 16,
    /// Machine error exception
    MachineError = 17,
    /// TLB refill exception
    TlbRefill = 63,
    /// Unknown exception
    Unknown = 0xFFFF,
}

impl ExceptionCode {
    /// Convert exception code number to enum
    pub fn from_code(code: u32) -> Self {
        match code {
            0 => ExceptionCode::Interrupt,
            1 => ExceptionCode::LoadPageInvalid,
            2 => ExceptionCode::StorePageInvalid,
            3 => ExceptionCode::FetchPageInvalid,
            4 => ExceptionCode::PageModification,
            5 => ExceptionCode::PageNonReadable,
            6 => ExceptionCode::PageNonWritable,
            7 => ExceptionCode::PageNonExecutable,
            8 => ExceptionCode::AddressErrorFetch,
            9 => ExceptionCode::AddressErrorMemory,
            11 => ExceptionCode::SystemCall,
            12 => ExceptionCode::Breakpoint,
            13 => ExceptionCode::InstructionUndefined,
            14 => ExceptionCode::InstructionPrivilege,
            15 => ExceptionCode::FloatingPointDisabled,
            16 => ExceptionCode::VectorInstructionsDisabled,
            17 => ExceptionCode::MachineError,
            63 => ExceptionCode::TlbRefill,
            _ => ExceptionCode::Unknown,
        }
    }

    /// Get human-readable description
    pub fn description(&self) -> &'static str {
        match self {
            ExceptionCode::Interrupt => "Interrupt",
            ExceptionCode::LoadPageInvalid => "Load operation page invalid",
            ExceptionCode::StorePageInvalid => "Store operation page invalid",
            ExceptionCode::FetchPageInvalid => "Instruction fetch page invalid",
            ExceptionCode::PageModification => "Page modification",
            ExceptionCode::PageNonReadable => "Page non-readable",
            ExceptionCode::PageNonWritable => "Page non-writable",
            ExceptionCode::PageNonExecutable => "Page non-executable",
            ExceptionCode::AddressErrorFetch => "Address error for instruction fetch",
            ExceptionCode::AddressErrorMemory => "Address error for memory access",
            ExceptionCode::SystemCall => "System call",
            ExceptionCode::Breakpoint => "Breakpoint",
            ExceptionCode::InstructionUndefined => "Instruction non-defined",
            ExceptionCode::InstructionPrivilege => "Instruction privilege error",
            ExceptionCode::FloatingPointDisabled => "Floating point disabled",
            ExceptionCode::VectorInstructionsDisabled => "128-bit vector instructions disabled",
            ExceptionCode::MachineError => "Machine error",
            ExceptionCode::TlbRefill => "TLB refill",
            ExceptionCode::Unknown => "Unknown exception",
        }
    }
}

/// Exception context structure (based on polyhal TrapFrame)
#[derive(Debug)]
#[repr(C)]
pub struct TrapFrame {
    /// General purpose registers (r0-r31)
    pub regs: [usize; 32],
    /// Pre-exception mode (PRMD)
    pub prmd: usize,
    /// Exception return address (ERA)
    pub era: usize,
}

impl TrapFrame {}

/// Main exception handler
#[no_mangle]
pub extern "C" fn loongarch_trap_handler(tf: &mut TrapFrame) {
    let estat: usize;
    let badv: usize;
    let badi: usize;

    unsafe {
        core::arch::asm!(
            "csrrd {estat}, 0x5",
            "csrrd {badv}, 0x7",
            "csrrd {badi}, 0x8",
            estat = out(reg) estat,
            badv = out(reg) badv,
            badi = out(reg) badi,
        );
    }

    let exc_code = ((estat >> 16) & 0x3F) as u32;
    let exception = ExceptionCode::from_code(exc_code);

    boot_error!("Exception: {} at 0x{:x}", exception.description(), tf.era);

    match exception {
        ExceptionCode::InstructionUndefined => {
            boot_error!("Bad instruction: 0x{:08x}", badi as u32);
        }
        ExceptionCode::FetchPageInvalid | ExceptionCode::LoadPageInvalid | ExceptionCode::StorePageInvalid => {
            boot_error!("Page fault: 0x{:x}", badv);
        }
        _ => {}
    }

    boot_error!("System halting");
    arch::power::shutdown();
}

/// Exception vector entry (based on polyhal trap_vector_base)
#[unsafe(naked)]
#[no_mangle]
#[link_section = ".text.exception"]
unsafe extern "C" fn trap_vector_base() {
    naked_asm!(
        r#"
        .balign 4096

        // Allocate space for TrapFrame (32 regs + 2 CSRs = 34 * 8 = 272 bytes)
        addi.d  $sp, $sp, -272

        // Save general purpose registers (based on polyhal SAVE_REGS)
        st.d    $ra, $sp,  1*8      // r1
        st.d    $tp, $sp,  2*8      // r2
        st.d    $sp, $sp,  3*8      // r3 (will be corrected later)
        st.d    $a0, $sp,  4*8      // r4
        st.d    $a1, $sp,  5*8      // r5
        st.d    $a2, $sp,  6*8      // r6
        st.d    $a3, $sp,  7*8      // r7
        st.d    $a4, $sp,  8*8      // r8
        st.d    $a5, $sp,  9*8      // r9
        st.d    $a6, $sp, 10*8      // r10
        st.d    $a7, $sp, 11*8      // r11
        st.d    $t0, $sp, 12*8      // r12
        st.d    $t1, $sp, 13*8      // r13
        st.d    $t2, $sp, 14*8      // r14
        st.d    $t3, $sp, 15*8      // r15
        st.d    $t4, $sp, 16*8      // r16
        st.d    $t5, $sp, 17*8      // r17
        st.d    $t6, $sp, 18*8      // r18
        st.d    $t7, $sp, 19*8      // r19
        st.d    $t8, $sp, 20*8      // r20
        st.d    $r21,$sp, 21*8      // r21
        st.d    $fp, $sp, 22*8      // r22
        st.d    $s0, $sp, 23*8      // r23
        st.d    $s1, $sp, 24*8      // r24
        st.d    $s2, $sp, 25*8      // r25
        st.d    $s3, $sp, 26*8      // r26
        st.d    $s4, $sp, 27*8      // r27
        st.d    $s5, $sp, 28*8      // r28
        st.d    $s6, $sp, 29*8      // r29
        st.d    $s7, $sp, 30*8      // r30
        st.d    $s8, $sp, 31*8      // r31

        // Fix sp value (add back the stack frame size)
        addi.d  $t0, $sp, 272
        st.d    $t0, $sp, 3*8       // Correct r3 (sp)

        // Clear r0 slot
        st.d    $zero, $sp, 0*8     // r0

        // Save CSRs
        csrrd   $t0, 0x1            // PRMD
        st.d    $t0, $sp, 32*8

        csrrd   $t0, 0x6            // ERA
        st.d    $t0, $sp, 33*8

        // Call trap handler
        move    $a0, $sp
        bl      {trap_handler}

        // Should not return, infinite loop
        1:
        b       1b
        "#,
        trap_handler = sym loongarch_trap_handler,
    );
}



/// TLB refill handler - handle page faults
#[unsafe(naked)]
#[no_mangle]
#[link_section = ".text.exception"]
unsafe extern "C" fn tlb_fill() {
    naked_asm!(
        // Save minimal registers for TLB refill handling
        "addi.d  $sp, $sp, -32",
        "st.d    $t0, $sp, 0",
        "st.d    $t1, $sp, 8",
        "st.d    $t2, $sp, 16",
        "st.d    $ra, $sp, 24",

        // Call TLB refill handler
        "bl      {tlb_refill_handler}",

        // Restore registers
        "ld.d    $t0, $sp, 0",
        "ld.d    $t1, $sp, 8",
        "ld.d    $t2, $sp, 16",
        "ld.d    $ra, $sp, 24",
        "addi.d  $sp, $sp, 32",

        // Return from TLB refill
        "ertn",

        tlb_refill_handler = sym handle_tlb_refill,
    );
}

/// Handle TLB refill exceptions
#[no_mangle]
extern "C" fn handle_tlb_refill() {
    let badv: usize;
    unsafe {
        core::arch::asm!(
            "csrrd {badv}, 0x7",
            badv = out(reg) badv,
        );
    }

    boot_error!("TLB refill: 0x{:x}", badv);
    arch::power::shutdown();
}

/// Initialize TLB and related CSRs
pub fn tlb_init(tlbrentry: usize) {


    unsafe {
        // Set page size to 4KB for all TLB entries
        core::arch::asm!(
            // TLBIDX - set page size
            "li.d    $t0, {ps}",
            "csrwr   $t0, 0x10",    // LOONGARCH_CSR_TLBIDX

            // STLBPS - set STLB page size
            "li.d    $t0, {ps}",
            "csrwr   $t0, 0x1E",    // LOONGARCH_CSR_STLBPS

            // TLBREHI - set page size
            "li.d    $t0, {ps}",
            "csrwr   $t0, 0x11",    // LOONGARCH_CSR_TLBREHI

            ps = const PS_4K,
            out("$t0") _,
        );

        // Set up page walk control registers
        // PWCL (Page Walk Control Low)
        let pwcl_val = (8 << 30) |                    // PTE width (64-bits)
                       (PAGE_SIZE_SHIFT << 25) |       // PT base
                       ((PAGE_SIZE_SHIFT - 3) << 20) | // PT width
                       ((PAGE_SIZE_SHIFT + PAGE_SIZE_SHIFT - 3) << 15) | // DIR1 base
                       ((PAGE_SIZE_SHIFT - 3) << 10);  // DIR1 width

        core::arch::asm!(
            "csrwr   {pwcl}, 0x1C",    // LOONGARCH_CSR_PWCL
            pwcl = in(reg) pwcl_val,
        );

        // PWCH (Page Walk Control High)
        let pwch_val = ((PAGE_SIZE_SHIFT + PAGE_SIZE_SHIFT - 3 + PAGE_SIZE_SHIFT - 3) << 6) | // DIR3 base
                       (PAGE_SIZE_SHIFT - 3);  // DIR3 width

        core::arch::asm!(
            "csrwr   {pwch}, 0x1D",    // LOONGARCH_CSR_PWCH
            pwch = in(reg) pwch_val,
        );

        // Set TLB refill entry point
        core::arch::asm!(
            "csrwr   {entry}, 0x88",   // LOONGARCH_CSR_TLBRENTRY
            entry = in(reg) tlbrentry,
        );
    }

    boot_info!("TLB ready");
}

/// Initialize LoongArch64 system
pub fn init_loongarch_system() {
    boot_info!("Init TLB, exceptions, DMW");

    tlb_init(tlb_fill as usize);

    unsafe {
        // Explicitly disable all vector and floating point extensions
        // EUEN register: FPE=0, SXE=0, ASXE=0, BTE=0
        core::arch::asm!(
            "li.w    $t0, 0x00",
            "csrwr   $t0, 0x2",        // LOONGARCH_CSR_EUEN
            out("$t0") _,
        );

        // Set exception configuration
        core::arch::asm!(
            "csrrd   $t0, 0x4",
            "li.d    $t1, 0x7",
            "andn    $t0, $t0, $t1",
            "csrwr   $t0, 0x4",
            out("$t0") _,
            out("$t1") _,
        );

        // Set exception entry
        let entry_addr = trap_vector_base as *const () as usize;
        core::arch::asm!(
            "csrwr   {entry}, 0xC",
            entry = in(reg) entry_addr,
        );
    }

    init_dmw();
    boot_info!("System ready");
}

/// Initialize DMW (Direct Mapped Window)
pub fn init_dmw() {
    unsafe {
        // DMW0: Uncached mapping
        core::arch::asm!(
            "ori         $t0, $zero, 0x1",
            "lu52i.d     $t0, $t0, -2048",
            "csrwr       $t0, 0x180",
            out("$t0") _,
        );

        // DMW1: Cached mapping
        core::arch::asm!(
            "ori         $t0, $zero, 0x11",
            "lu52i.d     $t0, $t0, -1792",
            "csrwr       $t0, 0x181",
            out("$t0") _,
        );
    }
    boot_info!("DMW ready");
}
