[build]
# Default target for RISC-V 64-bit
target = "riscv64gc-unknown-none-elf"

[target.riscv64gc-unknown-none-elf]
# Use rust-lld as the linker
linker = "rust-lld"
rustflags = [
    "-C", "link-arg=-Tboot/src/riscv64/linker_riscv64.ld",
]

[target.loongarch64-unknown-none]
# Use rust-lld as the linker for LoongArch
linker = "rust-lld"
rustflags = [
    "-C", "link-arg=-Tboot/src/loongarch64/linker_loongarch64.ld",
    # Disable all vector instructions to avoid 128-bit vector instruction exceptions
    "-C", "target-feature=-lsx,-lasx,-lvz",
    # Use the most basic LoongArch64 CPU without any extensions
    "-C", "target-cpu=loongarch64",
    # Force no vectorization
    "-C", "opt-level=1",
    "-C", "codegen-units=1",
    "-C", "panic=abort",
]

[unstable]
# Enable unstable features if needed
build-std = ["core", "alloc"]
build-std-features = ["compiler-builtins-mem"]
