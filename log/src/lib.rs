#![no_std]

//! Custom logging system for Echos OS
//!
//! This module provides a flexible logging system where each module can have
//! its own log level control. Supports different log levels and module-specific
//! enable/disable switches.

use core::fmt::{self, Write};

// Import compile-time switches
pub mod switch;

/// Log levels in order of severity
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord)]
#[repr(u8)]
pub enum LogLevel {
    Trace = 0,
    Debug = 1,
    Info = 2,
    Warn = 3,
    Error = 4,
    Fatal = 5,
}

impl LogLevel {
    /// Get the string representation of the log level
    pub const fn as_str(&self) -> &'static str {
        match self {
            LogLevel::Trace => "TRACE",
            LogLevel::Debug => "DEBUG",
            LogLevel::Info => "INFO",
            LogLevel::Warn => "WARN",
            LogLevel::Error => "ERROR",
            LogLevel::Fatal => "FATAL",
        }
    }

    /// Get the color code for the log level (ANSI colors)
    pub const fn color_code(&self) -> &'static str {
        match self {
            LogLevel::Trace => "\x1b[37m",    // White
            LogLevel::Debug => "\x1b[36m",    // Cyan
            LogLevel::Info => "\x1b[32m",     // Green
            LogLevel::Warn => "\x1b[33m",     // Yellow
            LogLevel::Error => "\x1b[31m",    // Red
            LogLevel::Fatal => "\x1b[35m",    // Magenta
        }
    }
}

/// Log module configuration
#[derive(Debug, Clone, Copy)]
pub struct LogModule {
    pub name: &'static str,
    pub enabled: bool,
    pub level: LogLevel,
    pub custom_color: Option<&'static str>,
}

impl LogModule {
    /// Create a new log module
    pub const fn new(name: &'static str, enabled: bool, level: LogLevel) -> Self {
        Self { name, enabled, level, custom_color: None }
    }

    /// Create a new log module with custom color
    pub const fn new_with_color(name: &'static str, enabled: bool, level: LogLevel, color: &'static str) -> Self {
        Self { name, enabled, level, custom_color: Some(color) }
    }

    /// Check if a log level should be printed for this module
    pub fn should_log(&self, level: LogLevel) -> bool {
        self.enabled && level >= self.level
    }

    /// Get the color for this module
    pub fn get_color(&self, level: LogLevel) -> &'static str {
        self.custom_color.unwrap_or_else(|| level.color_code())
    }
}

// Predefined log modules - each can be individually controlled
pub static mut DTB_LOG: LogModule = LogModule::new_with_color("DTB", true, LogLevel::Info, "\x1b[34m"); // Blue
pub static mut FRAME_LOG: LogModule = LogModule::new("FRAME", true, LogLevel::Info);
pub static mut MM_LOG: LogModule = LogModule::new("MM", true, LogLevel::Info);
pub static mut HEAP_LOG: LogModule = LogModule::new_with_color("HEAP", true, LogLevel::Info, "\x1b[35m"); // Magenta
pub static mut FS_LOG: LogModule = LogModule::new("FS", true, LogLevel::Info);
pub static mut NET_LOG: LogModule = LogModule::new("NET", true, LogLevel::Info);
pub static mut DRIVER_LOG: LogModule = LogModule::new("DRIVER", true, LogLevel::Info);
pub static mut KERNEL_LOG: LogModule = LogModule::new("KERNEL", true, LogLevel::Info);
pub static mut BOOT_LOG: LogModule = LogModule::new("BOOT", true, LogLevel::Info);
pub static mut CONSOLE_LOG: LogModule = LogModule::new("CONSOLE", true, LogLevel::Info);
pub static mut ARCH_LOG: LogModule = LogModule::new("ARCH", true, LogLevel::Info);
pub static mut CONFIG_LOG: LogModule = LogModule::new("CONFIG", true, LogLevel::Info);
pub static mut TIMER_LOG: LogModule = LogModule::new("TIMER", true, LogLevel::Info);
pub static mut IRQ_LOG: LogModule = LogModule::new("IRQ", true, LogLevel::Info);
pub static mut SYSCALL_LOG: LogModule = LogModule::new("SYSCALL", true, LogLevel::Info);
pub static mut TASK_LOG: LogModule = LogModule::new("TASK", true, LogLevel::Info);

/// Global log configuration
pub struct LogConfig {
    pub use_colors: bool,
    pub show_module: bool,
    pub show_level: bool,
}

impl Default for LogConfig {
    fn default() -> Self {
        Self {
            use_colors: true,
            show_module: true,
            show_level: true,
        }
    }
}

pub static mut LOG_CONFIG: LogConfig = LogConfig {
    use_colors: true,
    show_module: true,
    show_level: true,
};

/// Internal writer for log output
struct LogWriter;

impl Write for LogWriter {
    fn write_str(&mut self, s: &str) -> fmt::Result {
        // Use console module for output
        console::write_str(s);
        Ok(())
    }
}

/// Core logging function
pub fn log_with_module(module: &LogModule, level: LogLevel, args: fmt::Arguments) {
    if !module.should_log(level) {
        return;
    }

    let mut writer = LogWriter;
    let config = unsafe { &LOG_CONFIG };

    // Start with color if enabled
    if config.use_colors {
        let _ = write!(writer, "{}", module.get_color(level));
    }

    // Write opening bracket
    let _ = write!(writer, "[");

    // Write level if enabled
    if config.show_level {
        let _ = write!(writer, "{}", level.as_str());
        if config.show_module {
            let _ = write!(writer, " ");
        }
    }

    // Write module name if enabled
    if config.show_module {
        let _ = write!(writer, "{}", module.name);
    }

    // Write closing bracket and message
    let _ = write!(writer, "] {}", args);

    // Reset color if enabled
    if config.use_colors {
        let _ = write!(writer, "\x1b[0m");
    }

    // Add newline
    let _ = write!(writer, "\n");
}

/// Utility functions for controlling log modules
pub fn enable_module(module: &mut LogModule) {
    module.enabled = true;
}

pub fn disable_module(module: &mut LogModule) {
    module.enabled = false;
}

pub fn set_module_level(module: &mut LogModule, level: LogLevel) {
    module.level = level;
}

/// Compile-time controlled log macros
/// These macros check the const switches at compile time for zero runtime cost

// DTB (Device Tree Blob) logging macros
#[macro_export]
macro_rules! dtb_trace {
    ($($arg:tt)*) => {
        if $crate::switch::DTB_LOG {
            $crate::log_with_module(unsafe { &$crate::DTB_LOG }, $crate::LogLevel::Trace, format_args!($($arg)*))
        }
    };
}

#[macro_export]
macro_rules! dtb_debug {
    ($($arg:tt)*) => {
        if $crate::switch::DTB_LOG {
            $crate::log_with_module(unsafe { &$crate::DTB_LOG }, $crate::LogLevel::Debug, format_args!($($arg)*))
        }
    };
}

#[macro_export]
macro_rules! dtb_info {
    ($($arg:tt)*) => {
        if $crate::switch::DTB_LOG {
            $crate::log_with_module(unsafe { &$crate::DTB_LOG }, $crate::LogLevel::Info, format_args!($($arg)*))
        }
    };
}

#[macro_export]
macro_rules! dtb_warn {
    ($($arg:tt)*) => {
        if $crate::switch::DTB_LOG {
            $crate::log_with_module(unsafe { &$crate::DTB_LOG }, $crate::LogLevel::Warn, format_args!($($arg)*))
        }
    };
}

#[macro_export]
macro_rules! dtb_error {
    ($($arg:tt)*) => {
        if $crate::switch::DTB_LOG {
            $crate::log_with_module(unsafe { &$crate::DTB_LOG }, $crate::LogLevel::Error, format_args!($($arg)*))
        }
    };
}

// Frame allocator logging macros
#[macro_export]
macro_rules! frame_trace {
    ($($arg:tt)*) => {
        if $crate::switch::FRAME_LOG {
            $crate::log_with_module(unsafe { &$crate::FRAME_LOG }, $crate::LogLevel::Trace, format_args!($($arg)*))
        }
    };
}

#[macro_export]
macro_rules! frame_debug {
    ($($arg:tt)*) => {
        if $crate::switch::FRAME_LOG {
            $crate::log_with_module(unsafe { &$crate::FRAME_LOG }, $crate::LogLevel::Debug, format_args!($($arg)*))
        }
    };
}

#[macro_export]
macro_rules! frame_info {
    ($($arg:tt)*) => {
        if $crate::switch::FRAME_LOG {
            $crate::log_with_module(unsafe { &$crate::FRAME_LOG }, $crate::LogLevel::Info, format_args!($($arg)*))
        }
    };
}

#[macro_export]
macro_rules! frame_warn {
    ($($arg:tt)*) => {
        if $crate::switch::FRAME_LOG {
            $crate::log_with_module(unsafe { &$crate::FRAME_LOG }, $crate::LogLevel::Warn, format_args!($($arg)*))
        }
    };
}

#[macro_export]
macro_rules! frame_error {
    ($($arg:tt)*) => {
        if $crate::switch::FRAME_LOG {
            $crate::log_with_module(unsafe { &$crate::FRAME_LOG }, $crate::LogLevel::Error, format_args!($($arg)*))
        }
    };
}

// Kernel module macros
#[macro_export]
macro_rules! kernel_info {
    ($($arg:tt)*) => {
        if $crate::switch::KERNEL_LOG {
            $crate::log_with_module(unsafe { &$crate::KERNEL_LOG }, $crate::LogLevel::Info, format_args!($($arg)*))
        }
    };
}

#[macro_export]
macro_rules! kernel_warn {
    ($($arg:tt)*) => {
        if $crate::switch::KERNEL_LOG {
            $crate::log_with_module(unsafe { &$crate::KERNEL_LOG }, $crate::LogLevel::Warn, format_args!($($arg)*))
        }
    };
}

#[macro_export]
macro_rules! kernel_error {
    ($($arg:tt)*) => {
        if $crate::switch::KERNEL_LOG {
            $crate::log_with_module(unsafe { &$crate::KERNEL_LOG }, $crate::LogLevel::Error, format_args!($($arg)*))
        }
    };
}

// Boot module macros
#[macro_export]
macro_rules! boot_info {
    ($($arg:tt)*) => {
        if $crate::switch::BOOT_LOG {
            $crate::log_with_module(unsafe { &$crate::BOOT_LOG }, $crate::LogLevel::Info, format_args!($($arg)*))
        }
    };
}

#[macro_export]
macro_rules! boot_warn {
    ($($arg:tt)*) => {
        if $crate::switch::BOOT_LOG {
            $crate::log_with_module(unsafe { &$crate::BOOT_LOG }, $crate::LogLevel::Warn, format_args!($($arg)*))
        }
    };
}

#[macro_export]
macro_rules! boot_error {
    ($($arg:tt)*) => {
        if $crate::switch::BOOT_LOG {
            $crate::log_with_module(unsafe { &$crate::BOOT_LOG }, $crate::LogLevel::Error, format_args!($($arg)*))
        }
    };
}

// Memory management macros
#[macro_export]
macro_rules! mm_info {
    ($($arg:tt)*) => {
        if $crate::switch::MM_LOG {
            $crate::log_with_module(unsafe { &$crate::MM_LOG }, $crate::LogLevel::Info, format_args!($($arg)*))
        }
    };
}

#[macro_export]
macro_rules! mm_debug {
    ($($arg:tt)*) => {
        if $crate::switch::MM_LOG {
            $crate::log_with_module(unsafe { &$crate::MM_LOG }, $crate::LogLevel::Debug, format_args!($($arg)*))
        }
    };
}

// Heap allocator macros
#[macro_export]
macro_rules! heap_info {
    ($($arg:tt)*) => {
        if $crate::switch::HEAP_LOG {
            $crate::log_with_module(unsafe { &$crate::HEAP_LOG }, $crate::LogLevel::Info, format_args!($($arg)*))
        }
    };
}

#[macro_export]
macro_rules! heap_debug {
    ($($arg:tt)*) => {
        if $crate::switch::HEAP_LOG {
            $crate::log_with_module(unsafe { &$crate::HEAP_LOG }, $crate::LogLevel::Debug, format_args!($($arg)*))
        }
    };
}

#[macro_export]
macro_rules! heap_warn {
    ($($arg:tt)*) => {
        if $crate::switch::HEAP_LOG {
            $crate::log_with_module(unsafe { &$crate::HEAP_LOG }, $crate::LogLevel::Warn, format_args!($($arg)*))
        }
    };
}

#[macro_export]
macro_rules! heap_error {
    ($($arg:tt)*) => {
        if $crate::switch::HEAP_LOG {
            $crate::log_with_module(unsafe { &$crate::HEAP_LOG }, $crate::LogLevel::Error, format_args!($($arg)*))
        }
    };
}

// Driver macros
#[macro_export]
macro_rules! driver_info {
    ($($arg:tt)*) => {
        if $crate::switch::DRIVER_LOG {
            $crate::log_with_module(unsafe { &$crate::DRIVER_LOG }, $crate::LogLevel::Info, format_args!($($arg)*))
        }
    };
}

#[macro_export]
macro_rules! driver_error {
    ($($arg:tt)*) => {
        if $crate::switch::DRIVER_LOG {
            $crate::log_with_module(unsafe { &$crate::DRIVER_LOG }, $crate::LogLevel::Error, format_args!($($arg)*))
        }
    };
}

// Console module macros
#[macro_export]
macro_rules! console_info {
    ($($arg:tt)*) => {
        if $crate::switch::CONSOLE_LOG {
            $crate::log_with_module(unsafe { &$crate::CONSOLE_LOG }, $crate::LogLevel::Info, format_args!($($arg)*))
        }
    };
}

#[macro_export]
macro_rules! console_debug {
    ($($arg:tt)*) => {
        if $crate::switch::CONSOLE_LOG {
            $crate::log_with_module(unsafe { &$crate::CONSOLE_LOG }, $crate::LogLevel::Debug, format_args!($($arg)*))
        }
    };
}

// Architecture module macros
#[macro_export]
macro_rules! arch_info {
    ($($arg:tt)*) => {
        if $crate::switch::ARCH_LOG {
            $crate::log_with_module(unsafe { &$crate::ARCH_LOG }, $crate::LogLevel::Info, format_args!($($arg)*))
        }
    };
}

#[macro_export]
macro_rules! arch_error {
    ($($arg:tt)*) => {
        if $crate::switch::ARCH_LOG {
            $crate::log_with_module(unsafe { &$crate::ARCH_LOG }, $crate::LogLevel::Error, format_args!($($arg)*))
        }
    };
}

// Timer module macros
#[macro_export]
macro_rules! timer_info {
    ($($arg:tt)*) => {
        if $crate::switch::TIMER_LOG {
            $crate::log_with_module(unsafe { &$crate::TIMER_LOG }, $crate::LogLevel::Info, format_args!($($arg)*))
        }
    };
}

#[macro_export]
macro_rules! timer_debug {
    ($($arg:tt)*) => {
        if $crate::switch::TIMER_LOG {
            $crate::log_with_module(unsafe { &$crate::TIMER_LOG }, $crate::LogLevel::Debug, format_args!($($arg)*))
        }
    };
}


