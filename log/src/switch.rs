//! Compile-time log switches for Echos OS
//!
//! Set these constants to `true` or `false` to enable/disable logging
//! for specific modules at compile time. Disabled logs have zero runtime cost.

/// Device Tree Blob parsing logs
pub const DTB_LOG: bool = true;

/// Frame allocator logs
pub const FRAME_LOG: bool = true;

/// Kernel core logs
pub const KERNEL_LOG: bool = true;

/// Boot process logs
pub const BOOT_LOG: bool = true;

/// Memory management logs
pub const MM_LOG: bool = true;

/// Heap allocator logs
pub const HEAP_LOG: bool = true;

/// File system logs
pub const FS_LOG: bool = true;

/// Network stack logs
pub const NET_LOG: bool = true;

/// Device driver logs
pub const DRIVER_LOG: bool = true;

/// Console operation logs
pub const CONSOLE_LOG: bool = true;

/// Architecture-specific logs
pub const ARCH_LOG: bool = true;

/// Configuration logs
pub const CONFIG_LOG: bool = true;

/// Timer subsystem logs
pub const TIMER_LOG: bool = true;

/// Interrupt handling logs
pub const IRQ_LOG: bool = true;

/// System call logs
pub const SYSCALL_LOG: bool = true;

/// Task management logs
pub const TASK_LOG: bool = true;