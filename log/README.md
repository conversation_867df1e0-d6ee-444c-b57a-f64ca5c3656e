# Echos OS Custom Log System

A flexible, modular logging system for Echos OS that allows individual control over different subsystem logs.

## Features

- **Modular Control**: Each subsystem (DTB, Frame, MM, etc.) can be individually enabled/disabled
- **Level Control**: Each module can have its own log level (Trace, Debug, Info, Warn, <PERSON>rror, Fatal)
- **No External Dependencies**: Pure Rust implementation without external log crates
- **Colorized Output**: Optional ANSI color support for different log levels
- **Zero Runtime Cost**: Disabled logs have zero runtime overhead

## Available Modules

- `DTB_LOG` - Device Tree Blob parsing
- `FRAME_LOG` - Frame allocator
- `MM_LOG` - Memory management
- `FS_LOG` - File system
- `NET_LOG` - Network stack
- `DRIVER_LOG` - Device drivers
- `KERNEL_LOG` - Kernel core
- `BOOT_LOG` - Boot process
- `CONSOLE_LOG` - Console operations
- `ARCH_LOG` - Architecture-specific code
- `CONFIG_LOG` - Configuration
- `TIMER_LOG` - Timer subsystem
- `IRQ_LOG` - Interrupt handling
- `SYSCALL_LOG` - System calls
- `TASK_LOG` - Task management

## Usage

### Basic Logging

```rust
use log::*;

// Log with different levels
dtb_info!("Device tree parsing started");
dtb_debug!("Found {} nodes", node_count);
dtb_warn!("Unknown property: {}", prop_name);
dtb_error!("Failed to parse node: {}", error);

frame_info!("Frame allocator initialized");
frame_debug!("Allocated frame at 0x{:x}", addr);

kernel_info!("System ready");
```

### Controlling Modules

```rust
use log::*;

unsafe {
    // Disable a module completely
    disable_module(&mut DTB_LOG);
    
    // Enable a module
    enable_module(&mut FRAME_LOG);
    
    // Set log level for a module
    set_module_level(&mut MM_LOG, LogLevel::Debug);
    set_module_level(&mut DRIVER_LOG, LogLevel::Error);
}
```

### Global Configuration

```rust
use log::*;

unsafe {
    // Disable colors
    LOG_CONFIG.use_colors = false;
    
    // Hide module names
    LOG_CONFIG.show_module = false;
    
    // Hide log levels
    LOG_CONFIG.show_level = false;
}
```

## Log Levels

1. **Trace** - Very detailed information, typically only of interest when diagnosing problems
2. **Debug** - Debug information, useful during development
3. **Info** - General information about program execution
4. **Warn** - Warning messages for potentially harmful situations
5. **Error** - Error messages for error conditions
6. **Fatal** - Critical errors that may cause the program to abort

## Output Format

Default format: `[LEVEL MODULE] message`

Examples:
- `[INFO DTB] Device tree parsing started`
- `[DEBUG FRAME] Allocated frame at 0x1000`
- `[WARN KERNEL] Low memory warning`
- `[ERROR DRIVER] Failed to load network driver`

With colors enabled, each level has a different color:
- Trace: White
- Debug: Cyan  
- Info: Green
- Warn: Yellow
- Error: Red
- Fatal: Magenta

## Integration

Add to your `Cargo.toml`:

```toml
[dependencies]
log = { path = "../log" }
```

Then in your code:

```rust
use log::*;

fn init_subsystem() {
    kernel_info!("Initializing subsystem");
    // ... initialization code ...
    kernel_info!("Subsystem initialized successfully");
}
```

## Performance

- Disabled modules have zero runtime cost
- Log level checks are compile-time optimized
- Minimal memory footprint
- No heap allocations

## Thread Safety

The log system uses static mutable variables for configuration. In a multi-threaded environment, you should protect access to configuration changes with appropriate synchronization primitives.
