[workspace]
members = [
    "kernel",
    "boot",
    "console", "config", "arch", "log", "dtb", "mem",
]
resolver = "2"

[workspace.package]
version = "0.1.0"
edition = "2021"
authors = ["Echos OS Team"]
license = "MIT OR Apache-2.0"

[workspace.dependencies]
bitflags = "2.9.1"
riscv = "0.14.0"
sbi-rt = { version = "0.0.2", features = ["legacy"] }



[profile.release]
panic = "abort"
lto = true
codegen-units = 1
opt-level = "z"  # Optimize for size

[profile.dev]
panic = "abort"
opt-level = 1
