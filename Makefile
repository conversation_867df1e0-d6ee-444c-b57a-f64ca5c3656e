# Building
ARCH := riscv64
TEST := false
GUI  := false
SMP  := 1
FEATURES := 
BOOT_ARGS := 
GRUB_MKRESCUE := $(shell which grub-mkrescue || which grub2-mkrescue)

ifeq ($(ARCH), loongarch64)
export RUSTFLAGS := -C link-arg=-T$(PWD)/boot/src/$(ARCH)/linker_$(ARCH).ld -C target-feature=-lsx,-lasx,-lvz -C target-cpu=loongarch64 -C opt-level=1
else
export RUSTFLAGS := -C link-arg=-T$(PWD)/boot/src/$(ARCH)/linker_$(ARCH).ld
endif
QEMU_EXEC += qemu-system-$(ARCH)

ifeq ($(ARCH), x86_64)
  RUSTFLAGS += -Clink-arg=-no-pie
  TARGET := x86_64-unknown-none
  QEMU_EXEC += -machine q35 \
				-kernel $(KERNEL_ELF) \
				-cpu IvyBridge-v2
  BUS := pci
else ifeq ($(ARCH), riscv64)
  TARGET := riscv64gc-unknown-none-elf
  QEMU_EXEC += -machine virt -m 1G -kernel $(KERNEL_BIN)
else ifeq ($(ARCH), aarch64)
  TARGET := aarch64-unknown-none-softfloat
  QEMU_EXEC += -cpu cortex-a72 -machine virt -kernel $(KERNEL_BIN)
else ifeq ($(ARCH), loongarch64)
  TARGET := loongarch64-unknown-none
  QEMU_EXEC += -kernel $(KERNEL_ELF) -M virt -m 1G
  BUS := pci
else
  $(error "ARCH" must be one of "x86_64", "riscv64", "aarch64" or "loongarch64")
endif
KERNEL_ELF := target/$(TARGET)/release/kernel
KERNEL_BIN := $(KERNEL_ELF).bin

# Binutils
OBJDUMP := rust-objdump --arch-name=riscv64
OBJCOPY := rust-objcopy --binary-architecture=riscv64

ifneq ($(GUI), true)
QEMU_EXEC += -nographic
else
FEATURES += polyhal-boot/graphic polyhal/graphic
QEMU_EXEC += -serial stdio -vga std
endif 
QEMU_EXEC += -smp $(SMP)
QEMU_EXEC += -append "$(BOOT_ARGS)"

# Logging arguments for runlog mode
ifneq ($(SMP), 1)
QEMU_LOG_ARGS := -D qemu-%d.log -d in_asm,int,pcall,cpu_reset,tid,guest_errors
else
QEMU_LOG_ARGS := -D qemu.log -d in_asm,int,pcall,cpu_reset,guest_errors
endif

build: $(KERNEL_BIN) 

$(KERNEL_BIN): kernel
	@$(OBJCOPY) $(KERNEL_ELF) --strip-all -O binary $@

kernel:
	@echo Platform: $(ARCH)
	@cargo build --release --target $(TARGET) -p kernel

clean:
	@cargo clean

run: build
	@echo "Starting QEMU for $(ARCH) (no logging)..."
	@echo "Press Ctrl+A then X to exit QEMU"
	$(QEMU_EXEC)

runlog: build
	@echo "Starting QEMU for $(ARCH) with logging..."
	@echo "Press Ctrl+A then X to exit QEMU"
	@echo "Logs will be saved to qemu.log or qemu-*.log"
	rm -f qemu-*.log
	$(QEMU_EXEC) $(QEMU_LOG_ARGS)

debug: build
	$(QEMU_EXEC) -s -S

test:
	make ARCH=aarch64 run
	make ARCH=riscv64 run
	make ARCH=x86_64 run
	make ARCH=loongarch64 run

iso: build
	cp $(KERNEL_ELF) iso/example
	$(GRUB_MKRESCUE) -o bootable.iso iso

boot-iso: iso
	qemu-system-x86_64 -cdrom bootable.iso -serial stdio -vga vmware

gdb:
	gdb \
	-ex 'file $(KERNEL_ELF)' \
	-ex 'set arch x86_64' \
	-ex 'target remote localhost:1234'

.PHONY: build env kernel clean clean-log run-inner
