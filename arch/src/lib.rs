#![no_std]

//! Architecture-specific implementations for Echos OS
//!
//! This crate provides architecture-specific functionality including:
//! - Power management (shutdown, reboot)
//! - Low-level hardware abstractions
//! - Architecture-specific constants and utilities

// Architecture-specific modules
#[cfg(target_arch = "riscv64")]
pub mod riscv64;
#[cfg(target_arch = "riscv64")]
pub use riscv64 as arch;

#[cfg(target_arch = "loongarch64")]
pub mod loongarch64;
#[cfg(target_arch = "loongarch64")]
pub use loongarch64 as arch;

// Re-export power management functions for convenience
pub mod power {
    #[cfg(target_arch = "riscv64")]
    pub use crate::riscv64::power::*;

    #[cfg(target_arch = "loongarch64")]
    pub use crate::loongarch64::power::*;
}