//! LoongArch64 power management implementation

use config::VIRT_ADDR_OFFSET;
use core::arch::asm;

/// Virtual address helper macro
macro_rules! va {
    ($addr:expr) => {
        VirtAddr($addr)
    };
}

/// Virtual address wrapper
#[derive(<PERSON><PERSON>, Co<PERSON>, Debug)]
pub struct VirtAddr(pub usize);

impl VirtAddr {
    /// Get mutable pointer to the virtual address
    #[inline]
    pub fn get_mut_ptr<T>(&self) -> *mut T {
        self.0 as *mut T
    }
}

/// LoongArch64 assembly functions
pub mod loongarch64_asm {
    use core::arch::asm;

    /// Enter idle state
    #[inline]
    pub unsafe fn idle() -> ! {
        loop {
            asm!("idle 0", options(nomem, nostack));
        }
    }
}

/// Shutdown the system using LoongArch64 ACPI GED (Generic Event Device)
#[inline]
pub fn shutdown() -> ! {
    let ged_addr = va!(0x100E001C | VIRT_ADDR_OFFSET);

    // Write to ACPI GED register to trigger shutdown
    unsafe {
        ged_addr.get_mut_ptr::<u8>().write_volatile(0x34);
    }

    // Enter idle state
    unsafe {
        loongarch64_asm::idle();
    }

    unreachable!()
}